from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import Inventory, Warehouse, Ingredient, StorageLocation, InventoryAlert
from app.utils.school_required import school_required
from app import db, csrf
from datetime import datetime, date, timedelta
from sqlalchemy import func, text
import json

inventory_bp = Blueprint('inventory', __name__)

def safe_get_int_param(param_name, default=None):
    """安全地获取整数参数，如果转换失败返回默认值"""
    try:
        value = request.args.get(param_name)
        if value is None or value == '':
            return default
        return int(value)
    except (ValueError, TypeError):
        current_app.logger.warning(f"无效的{param_name}参数: {request.args.get(param_name)}")
        return default

def get_ingredient_flow_status(inventory):
    """获取食材流转状态信息"""
    try:
        from app.models import StockInItem, StockOutItem

        # 检查是否有出库记录
        has_stock_out = StockOutItem.query.filter_by(
            ingredient_id=inventory.ingredient_id,
            batch_number=inventory.batch_number
        ).first() is not None

        # 检查当前库存数量
        current_quantity = float(inventory.quantity) if inventory.quantity else 0

        # 判断流转状态
        if current_quantity > 0:
            if has_stock_out:
                return {
                    'status': 'partially_consumed',
                    'label': '部分消耗',
                    'class': 'warning',
                    'description': f'剩余 {current_quantity}{inventory.unit}'
                }
            else:
                return {
                    'status': 'available',
                    'label': '有库存',
                    'class': 'success',
                    'description': f'{current_quantity}{inventory.unit}'
                }
        else:
            if has_stock_out:
                return {
                    'status': 'fully_consumed',
                    'label': '已消耗完',
                    'class': 'secondary',
                    'description': '已完全消耗'
                }
            else:
                return {
                    'status': 'empty',
                    'label': '库存为空',
                    'class': 'danger',
                    'description': '无库存记录'
                }

    except Exception as e:
        current_app.logger.error(f"计算食材流转状态时出错: {str(e)}")
        return {
            'status': 'unknown',
            'label': '状态未知',
            'class': 'secondary',
            'description': '无法确定状态'
        }

@inventory_bp.route('/inventory')
@login_required
@school_required
def index(user_area):
    """库存列表页面"""
    # 使用 @school_required 装饰器传入的用户学校区域
    # 获取当前用户可访问的区域（保持兼容性，支持多学校用户）
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数，使用安全的参数获取函数
    page = safe_get_int_param('page', 1)
    per_page = current_app.config['ITEMS_PER_PAGE']
    warehouse_id = safe_get_int_param('warehouse_id')
    ingredient_id = safe_get_int_param('ingredient_id')
    status = request.args.get('status', '')
    expiry_days = safe_get_int_param('expiry_days')
    storage_location_id = safe_get_int_param('storage_location_id')
    view_type = request.args.get('view_type', 'detail')  # 'detail' 或 'summary'
    show_empty = request.args.get('show_empty', '0')  # 是否显示已用完的库存，默认不显示

    # 构建查询
    if view_type == 'detail':
        # 详细视图 - 使用原生SQL查询避免数据类型转换问题
        sql = text("""
            SELECT
                i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
                i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
                i.supplier_id, i.status, i.notes, i.created_at, i.updated_at,
                COUNT(*) OVER() as total_count
            FROM
                inventories i
            JOIN
                warehouses w ON w.id = i.warehouse_id
        """)

        # 构建查询条件
        where_clauses = []

        # 处理area_ids参数 - 使用字符串拼接而不是参数绑定
        if len(area_ids) == 1:
            where_clauses.append(f"w.area_id = {area_ids[0]}")
        else:
            area_ids_str = ','.join(str(id) for id in area_ids)
            where_clauses.append(f"w.area_id IN ({area_ids_str})")

        # 其他参数使用命名参数
        params = {}

        if warehouse_id:
            where_clauses.append("i.warehouse_id = :warehouse_id")
            params['warehouse_id'] = warehouse_id
        if ingredient_id:
            where_clauses.append("i.ingredient_id = :ingredient_id")
            params['ingredient_id'] = ingredient_id
        if status:
            where_clauses.append("i.status = :status")
            params['status'] = status
        elif show_empty == '0':  # 如果没有指定状态且不显示已用完的库存
            where_clauses.append("i.status = :normal_status")
            where_clauses.append("i.quantity > :min_quantity")
            params['normal_status'] = '正常'
            params['min_quantity'] = 0.0

            # 记录查询条件
            current_app.logger.info(f"库存查询条件: 状态=正常, 数量>0")
        if expiry_days:
            expiry_date = date.today() + timedelta(days=expiry_days)
            where_clauses.append("i.expiry_date <= :expiry_date")
            params['expiry_date'] = expiry_date
        if storage_location_id:
            where_clauses.append("i.storage_location_id = :storage_location_id")
            params['storage_location_id'] = storage_location_id

        # 添加WHERE子句
        if where_clauses:
            sql = text(sql.text + " WHERE " + " AND ".join(where_clauses))

        # 添加排序和分页
        offset = (page - 1) * per_page
        sql = text(sql.text + f" ORDER BY i.expiry_date OFFSET {offset} ROWS FETCH NEXT {per_page} ROWS ONLY")

        # 执行查询
        result = db.session.execute(sql, params)
        rows = result.fetchall()

        # 计算总记录数
        total_count = rows[0].total_count if rows else 0

        # 创建自定义分页对象
        class CustomPagination:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.pages = (total + per_page - 1) // per_page if per_page else 0
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1
                self.next_num = page + 1

            def __len__(self):
                return len(self.items)

            def iter_pages(self, left_edge=2, left_current=2, right_current=5, right_edge=2):
                last = 0
                for num in range(1, self.pages + 1):
                    if num <= left_edge or \
                       (num > self.page - left_current - 1 and num < self.page + right_current) or \
                       num > self.pages - right_edge:
                        if last + 1 != num:
                            yield None
                        yield num
                        last = num

        # 获取库存记录
        inventories = []
        for row in rows:
            # 创建库存对象
            inventory = Inventory(
                id=row.id,
                warehouse_id=row.warehouse_id,
                storage_location_id=row.storage_location_id,
                ingredient_id=row.ingredient_id,
                batch_number=row.batch_number,
                quantity=row.quantity,
                unit=row.unit,
                production_date=row.production_date,
                expiry_date=row.expiry_date,
                supplier_id=row.supplier_id,
                status=row.status,
                notes=row.notes,
                created_at=row.created_at,
                updated_at=row.updated_at
            )

            # 加载关联对象
            inventory.warehouse = Warehouse.query.get(row.warehouse_id)
            inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
            inventory.ingredient = Ingredient.query.get(row.ingredient_id)

            # 计算食材流转状态
            inventory.flow_status = get_ingredient_flow_status(inventory)

            inventories.append(inventory)

        # 创建分页对象
        pagination = CustomPagination(inventories, page, per_page, total_count)

        # 获取仓库列表
        warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

        # 获取食材列表
        ingredients = Ingredient.query.all()

        # 获取存储位置列表
        storage_locations = []
        if warehouse_id:
            storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id).all()

        return render_template('inventory/index.html',
                              inventories=inventories,
                              pagination=pagination,
                              warehouses=warehouses,
                              ingredients=ingredients,
                              storage_locations=storage_locations,
                              warehouse_id=warehouse_id,
                              ingredient_id=ingredient_id,
                              status=status,
                              expiry_days=expiry_days,
                              storage_location_id=storage_location_id,
                              view_type=view_type,
                              show_empty=show_empty)
    else:
        # 汇总视图 - 按食材汇总库存
        # 使用 SQLAlchemy 的 func 进行分组查询
        # 使用原始SQL语句进行查询，避免对nvarchar(max)类型使用聚合函数
        # 构建SQL查询
        sql_base = """
        SELECT
            inventories.ingredient_id AS inventories_ingredient_id,
            ingredients.name AS ingredient_name,
            ingredients.category AS ingredient_category,
            SUM(CAST(inventories.quantity AS DECIMAL(18, 2))) AS total_quantity,
            MAX(CAST(inventories.unit AS nvarchar(50))) AS unit
        FROM
            inventories
            JOIN ingredients ON ingredients.id = inventories.ingredient_id
            JOIN warehouses ON warehouses.id = inventories.warehouse_id
        WHERE
        """

        # 处理area_ids参数 - 使用字符串拼接
        if len(area_ids) == 1:
            area_condition = f" warehouses.area_id = {area_ids[0]}"
        else:
            area_ids_str = ','.join(str(id) for id in area_ids)
            area_condition = f" warehouses.area_id IN ({area_ids_str})"

        # 添加状态和数量条件
        status_value = status or '正常'
        # 如果状态是"正常"，则只显示数量大于0的记录
        min_quantity_value = 0 if show_empty == '1' or status == '已用完' else 0.001

        # 记录查询条件
        current_app.logger.info(f"库存汇总查询条件: 状态={status_value}, 最小数量={min_quantity_value}")

        sql_conditions = f"{area_condition} AND inventories.status = :status AND inventories.quantity > :min_quantity"

        # 完整SQL
        sql_full = sql_base + sql_conditions + """
        GROUP BY
            inventories.ingredient_id, ingredients.name, ingredients.category
        ORDER BY
            ingredients.name
        """

        sql = text(sql_full)

        # 构建参数
        params = {
            'status': status_value,
            'min_quantity': min_quantity_value
        }

        # 添加额外的过滤条件
        additional_conditions = []
        if warehouse_id:
            additional_conditions.append("inventories.warehouse_id = :warehouse_id")
            params['warehouse_id'] = warehouse_id
        if ingredient_id:
            additional_conditions.append("inventories.ingredient_id = :ingredient_id")
            params['ingredient_id'] = ingredient_id
        if storage_location_id:
            additional_conditions.append("inventories.storage_location_id = :storage_location_id")
            params['storage_location_id'] = storage_location_id

        # 如果有额外的过滤条件，修改SQL语句
        if additional_conditions:
            additional_where = " AND " + " AND ".join(additional_conditions)
            # 在GROUP BY前添加额外条件
            sql_parts = sql.text.split("GROUP BY")
            sql_with_where = sql_parts[0] + additional_where + " GROUP BY" + sql_parts[1]
            sql = text(sql_with_where)

        # 执行查询
        result = db.session.execute(sql, params)
        inventory_summary = result.fetchall()



        # 获取仓库列表
        warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

        # 获取食材列表
        ingredients = Ingredient.query.all()

        # 获取存储位置列表
        storage_locations = []
        if warehouse_id:
            storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id).all()

        return render_template('inventory/summary.html',
                              inventory_summary=inventory_summary,
                              warehouses=warehouses,
                              ingredients=ingredients,
                              storage_locations=storage_locations,
                              warehouse_id=warehouse_id,
                              ingredient_id=ingredient_id,
                              storage_location_id=storage_location_id,
                              view_type=view_type,
                              show_empty=show_empty)

@inventory_bp.route('/inventory/detail/<int:id>')
@login_required
def detail(id):
    """查看库存详情"""
    try:
        # 获取库存记录
        inventory = Inventory.query.get_or_404(id)

        # 检查用户是否有权限查看
        if not current_user.can_access_area_by_id(inventory.warehouse.area_id):
            flash('您没有权限查看该库存', 'danger')
            return redirect(url_for('inventory.index'))

        # 导入溯源相关模型
        from app.models import StockInItem, StockIn, StockOutItem, ConsumptionPlan, StockInDocument
        from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow

        # 通过批次号查询入库明细
        stock_in_items = StockInItem.query.filter_by(batch_number=inventory.batch_number).all()

        # 获取入库单
        stock_ins = []
        if stock_in_items:
            for item in stock_in_items:
                if item.stock_in and item.stock_in not in stock_ins:
                    stock_ins.append(item.stock_in)

        # 获取检验检疫证明
        certificates = []
        for stock_in in stock_ins:
            try:
                if hasattr(stock_in, 'documents') and stock_in.documents:
                    for doc in stock_in.documents:
                        if doc not in certificates:
                            certificates.append(doc)
            except Exception as e:
                current_app.logger.error(f"获取检验检疫证明失败: {str(e)}")

        # 获取出库记录和关联的消耗计划
        stock_out_items = []
        consumption_plans = []
        try:
            # 获取使用该批次号的出库明细
            stock_out_items = StockOutItem.query.filter_by(batch_number=inventory.batch_number).all()
            for item in stock_out_items:
                if item.stock_out and item.stock_out.consumption_plan and item.stock_out.consumption_plan not in consumption_plans:
                    consumption_plans.append(item.stock_out.consumption_plan)
        except Exception as e:
            current_app.logger.error(f"获取出库记录和关联消耗计划失败: {str(e)}")

        # 获取溯源信息
        material_batch = None
        trace_documents = []
        batch_flows = []
        try:
            # 通过批次号查询溯源批次
            material_batch = MaterialBatch.query.filter_by(batch_number=inventory.batch_number).first()
            if material_batch:
                # 获取溯源文档
                trace_documents = TraceDocument.query.filter_by(batch_id=material_batch.id).all()
                # 获取批次流水
                batch_flows = BatchFlow.query.filter_by(batch_id=material_batch.id).all()
        except Exception as e:
            current_app.logger.error(f"获取溯源信息失败: {str(e)}")

        # 记录日志
        current_app.logger.info(f"查看库存详情: ID={id}, 批次号={inventory.batch_number}")

        return render_template('inventory/detail.html',
                              inventory=inventory,
                              stock_in_items=stock_in_items,
                              stock_ins=stock_ins,
                              certificates=certificates,
                              stock_out_items=stock_out_items,
                              consumption_plans=consumption_plans,
                              material_batch=material_batch,
                              trace_documents=trace_documents,
                              batch_flows=batch_flows)
    except Exception as e:
        current_app.logger.error(f"查看库存详情失败: {str(e)}")
        flash(f'查看库存详情失败: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))

@inventory_bp.route('/inventory/get-storage-locations')
@login_required
def get_storage_locations():
    """获取仓库的存储位置列表（AJAX）"""
    warehouse_id = safe_get_int_param('warehouse_id')

    if not warehouse_id:
        return jsonify([])

    # 获取存储位置列表
    storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id, status='正常').all()

    # 转换为JSON格式
    locations = [{'id': loc.id, 'name': f"{loc.name} ({loc.location_code})"} for loc in storage_locations]

    return jsonify(locations)

@inventory_bp.route('/inventory/ingredient/<int:id>')
@login_required
@school_required
def ingredient_inventory(id, user_area):
    """查看某个食材的库存情况"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取食材信息
    ingredient = Ingredient.query.get_or_404(id)

    # 获取查询参数，添加错误处理
    show_empty = request.args.get('show_empty', '0')

    # 使用原生SQL查询避免数据类型转换问题
    sql = text("""
        SELECT
            i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
            i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
            i.supplier_id, i.status, i.notes, i.created_at, i.updated_at
        FROM
            inventories i
        JOIN
            warehouses w ON w.id = i.warehouse_id
        WHERE
            i.ingredient_id = :ingredient_id
    """)

    # 处理area_ids参数 - 使用字符串拼接
    if len(area_ids) == 1:
        sql = text(sql.text + f" AND w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        sql = text(sql.text + f" AND w.area_id IN ({area_ids_str})")

    # 构建查询参数
    params = {
        'ingredient_id': id
    }

    # 根据参数决定是否显示已用完的库存
    if show_empty == '0':
        sql = text(sql.text + """
            AND i.status = :status
            AND i.quantity > :min_quantity
        """)
        params['status'] = '正常'
        params['min_quantity'] = 0.0

    # 添加排序
    sql = text(sql.text + " ORDER BY i.expiry_date")

    # 执行查询
    result = db.session.execute(sql, params)
    rows = result.fetchall()

    # 获取该食材的库存列表
    inventories = []
    for row in rows:
        # 创建库存对象
        inventory = Inventory(
            id=row.id,
            warehouse_id=row.warehouse_id,
            storage_location_id=row.storage_location_id,
            ingredient_id=row.ingredient_id,
            batch_number=row.batch_number,
            quantity=row.quantity,
            unit=row.unit,
            production_date=row.production_date,
            expiry_date=row.expiry_date,
            supplier_id=row.supplier_id,
            status=row.status,
            notes=row.notes,
            created_at=row.created_at,
            updated_at=row.updated_at
        )

        # 加载关联对象
        inventory.warehouse = Warehouse.query.get(row.warehouse_id)
        inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
        inventory.ingredient = Ingredient.query.get(row.ingredient_id)

        inventories.append(inventory)

    # 计算总库存 - 确保将字符串转换为浮点数
    total_quantity = sum(float(inv.quantity) if isinstance(inv.quantity, str) else inv.quantity for inv in inventories)

    # 获取该食材的库存预警设置
    alerts = InventoryAlert.query.filter_by(ingredient_id=id).all()

    return render_template('inventory/ingredient.html',
                          ingredient=ingredient,
                          inventories=inventories,
                          total_quantity=total_quantity,
                          alerts=alerts,
                          show_empty=show_empty)

@inventory_bp.route('/inventory/check-expiry')
@login_required
@school_required
def check_expiry(user_area):
    """检查临期和过期库存"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数，使用安全的参数获取函数
    days = safe_get_int_param('days', 7)  # 默认7天内过期
    warehouse_id = safe_get_int_param('warehouse_id')

    # 计算临期日期
    expiry_date = date.today() + timedelta(days=days)

    # 使用原生SQL查询临期库存，避免数据类型转换问题
    sql_expiring = text("""
        SELECT
            i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
            i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
            i.supplier_id, i.status, i.notes, i.created_at, i.updated_at
        FROM
            inventories i
        JOIN
            warehouses w ON w.id = i.warehouse_id
        WHERE
    """)

    # 处理area_ids参数 - 使用字符串拼接
    if len(area_ids) == 1:
        sql_expiring = text(sql_expiring.text + f" w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        sql_expiring = text(sql_expiring.text + f" w.area_id IN ({area_ids_str})")

    # 添加其他条件
    sql_expiring = text(sql_expiring.text + """
            AND i.status = :status
            AND i.quantity > :min_quantity
            AND CONVERT(DATE, i.expiry_date) <= CONVERT(DATE, :expiry_date)
    """)

    # 构建查询参数 - 将日期转换为字符串格式
    params_expiring = {
        'status': '正常',
        'min_quantity': 0.0,
        'expiry_date': expiry_date.strftime('%Y-%m-%d')
    }

    # 应用仓库过滤
    if warehouse_id:
        sql_expiring = text(sql_expiring.text + " AND i.warehouse_id = :warehouse_id")
        params_expiring['warehouse_id'] = warehouse_id

    # 添加排序
    sql_expiring = text(sql_expiring.text + " ORDER BY i.expiry_date")

    # 执行查询
    result_expiring = db.session.execute(sql_expiring, params_expiring)
    rows_expiring = result_expiring.fetchall()

    # 获取临期库存列表
    expiring_inventories = []
    for row in rows_expiring:
        # 创建库存对象
        inventory = Inventory(
            id=row.id,
            warehouse_id=row.warehouse_id,
            storage_location_id=row.storage_location_id,
            ingredient_id=row.ingredient_id,
            batch_number=row.batch_number,
            quantity=row.quantity,
            unit=row.unit,
            production_date=row.production_date,
            expiry_date=row.expiry_date,
            supplier_id=row.supplier_id,
            status=row.status,
            notes=row.notes,
            created_at=row.created_at,
            updated_at=row.updated_at
        )

        # 加载关联对象
        inventory.warehouse = Warehouse.query.get(row.warehouse_id)
        inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
        inventory.ingredient = Ingredient.query.get(row.ingredient_id)

        # 计算剩余天数
        try:
            if isinstance(row.expiry_date, str):
                expiry_date_obj = datetime.strptime(row.expiry_date, '%Y-%m-%d').date()
            else:
                expiry_date_obj = row.expiry_date

            days_remaining = (expiry_date_obj - date.today()).days
            inventory.days_remaining = max(0, days_remaining)  # 确保不为负数
        except:
            inventory.days_remaining = 0

        expiring_inventories.append(inventory)

    # 使用原生SQL查询已过期库存，避免数据类型转换问题
    sql_expired = text("""
        SELECT
            i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
            i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
            i.supplier_id, i.status, i.notes, i.created_at, i.updated_at
        FROM
            inventories i
        JOIN
            warehouses w ON w.id = i.warehouse_id
        WHERE
    """)

    # 处理area_ids参数 - 使用字符串拼接
    if len(area_ids) == 1:
        sql_expired = text(sql_expired.text + f" w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        sql_expired = text(sql_expired.text + f" w.area_id IN ({area_ids_str})")

    # 添加其他条件
    sql_expired = text(sql_expired.text + """
            AND i.status = :status
            AND i.quantity > :min_quantity
            AND CONVERT(DATE, i.expiry_date) < CONVERT(DATE, :today)
    """)

    # 构建查询参数 - 将日期转换为字符串格式
    params_expired = {
        'status': '正常',
        'min_quantity': 0.0,
        'today': date.today().strftime('%Y-%m-%d')
    }

    # 应用仓库过滤
    if warehouse_id:
        sql_expired = text(sql_expired.text + " AND i.warehouse_id = :warehouse_id")
        params_expired['warehouse_id'] = warehouse_id

    # 添加排序
    sql_expired = text(sql_expired.text + " ORDER BY i.expiry_date")

    # 执行查询
    result_expired = db.session.execute(sql_expired, params_expired)
    rows_expired = result_expired.fetchall()

    # 获取已过期库存列表
    expired_inventories = []
    for row in rows_expired:
        # 创建库存对象
        inventory = Inventory(
            id=row.id,
            warehouse_id=row.warehouse_id,
            storage_location_id=row.storage_location_id,
            ingredient_id=row.ingredient_id,
            batch_number=row.batch_number,
            quantity=row.quantity,
            unit=row.unit,
            production_date=row.production_date,
            expiry_date=row.expiry_date,
            supplier_id=row.supplier_id,
            status=row.status,
            notes=row.notes,
            created_at=row.created_at,
            updated_at=row.updated_at
        )

        # 加载关联对象
        inventory.warehouse = Warehouse.query.get(row.warehouse_id)
        inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
        inventory.ingredient = Ingredient.query.get(row.ingredient_id)

        # 计算过期天数
        try:
            if isinstance(row.expiry_date, str):
                expiry_date_obj = datetime.strptime(row.expiry_date, '%Y-%m-%d').date()
            else:
                expiry_date_obj = row.expiry_date

            days_expired = (date.today() - expiry_date_obj).days
            inventory.days_expired = days_expired
        except:
            inventory.days_expired = 0

        expired_inventories.append(inventory)

    # 获取仓库列表
    warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

    return render_template('inventory/expiry.html',
                          expiring_inventories=expiring_inventories,
                          expired_inventories=expired_inventories,
                          days=days,
                          warehouse_id=warehouse_id,
                          warehouses=warehouses)

# 库存打印功能
@inventory_bp.route('/inventory/print')
@login_required
@school_required
def print_inventory(user_area):
    """打印库存报表"""

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    warehouse_id = safe_get_int_param('warehouse_id')
    ingredient_id = safe_get_int_param('ingredient_id')
    status = request.args.get('status', '正常')
    expiry_days = safe_get_int_param('expiry_days')
    storage_location_id = safe_get_int_param('storage_location_id')
    show_empty = request.args.get('show_empty', '0')

    # 使用原生SQL查询
    sql = text("""
        SELECT
            i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
            i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
            i.supplier_id, i.status, i.notes, i.created_at, i.updated_at
        FROM
            inventories i
        JOIN
            warehouses w ON w.id = i.warehouse_id
    """)

    # 构建查询条件
    where_clauses = []
    params = {}

    # 处理area_ids参数
    if len(area_ids) == 1:
        where_clauses.append(f"w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        where_clauses.append(f"w.area_id IN ({area_ids_str})")

    if warehouse_id:
        where_clauses.append("i.warehouse_id = :warehouse_id")
        params['warehouse_id'] = warehouse_id
    if ingredient_id:
        where_clauses.append("i.ingredient_id = :ingredient_id")
        params['ingredient_id'] = ingredient_id
    if status:
        where_clauses.append("i.status = :status")
        params['status'] = status
    elif show_empty == '0':
        where_clauses.append("i.status = :normal_status")
        where_clauses.append("i.quantity > :min_quantity")
        params['normal_status'] = '正常'
        params['min_quantity'] = 0.0
    if expiry_days:
        expiry_date = date.today() + timedelta(days=expiry_days)
        where_clauses.append("i.expiry_date <= :expiry_date")
        params['expiry_date'] = expiry_date
    if storage_location_id:
        where_clauses.append("i.storage_location_id = :storage_location_id")
        params['storage_location_id'] = storage_location_id

    # 添加WHERE子句
    if where_clauses:
        sql = text(sql.text + " WHERE " + " AND ".join(where_clauses))

    # 添加排序
    sql = text(sql.text + " ORDER BY i.expiry_date, i.ingredient_id")

    # 执行查询
    result = db.session.execute(sql, params)
    rows = result.fetchall()

    # 获取库存记录
    inventories = []
    for row in rows:
        inventory = Inventory(
            id=row.id,
            warehouse_id=row.warehouse_id,
            storage_location_id=row.storage_location_id,
            ingredient_id=row.ingredient_id,
            batch_number=row.batch_number,
            quantity=row.quantity,
            unit=row.unit,
            production_date=row.production_date,
            expiry_date=row.expiry_date,
            supplier_id=row.supplier_id,
            status=row.status,
            notes=row.notes,
            created_at=row.created_at,
            updated_at=row.updated_at
        )

        # 加载关联对象
        inventory.warehouse = Warehouse.query.get(row.warehouse_id)
        inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
        inventory.ingredient = Ingredient.query.get(row.ingredient_id)

        inventories.append(inventory)

    # 获取筛选信息
    filter_info = {
        'warehouse': Warehouse.query.get(warehouse_id).name if warehouse_id else '全部仓库',
        'ingredient': Ingredient.query.get(ingredient_id).name if ingredient_id else '全部食材',
        'status': status or '全部状态',
        'storage_location': StorageLocation.query.get(storage_location_id).name if storage_location_id else '全部位置'
    }

    return render_template('inventory/print_inventory.html',
                          inventories=inventories,
                          filter_info=filter_info,
                          print_date=datetime.now())

@inventory_bp.route('/inventory/item/<int:id>/label/print')
@csrf.exempt
@login_required
def print_item_label(id):
    """打印单个库存项标签"""
    inventory = Inventory.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(inventory.warehouse.area_id):
        flash('您没有权限打印该库存标签', 'danger')
        return redirect(url_for('inventory.index'))

    # 生成溯源URL（类似入库标签）
    trace_url = url_for('food_trace.qr_trace_direct',
                       batch_number=inventory.batch_number,
                       ingredient_id=inventory.ingredient_id,
                       _external=True)

    # 生成二维码
    from app.routes.stock_in import generate_qr_code
    qr_code_base64 = generate_qr_code(trace_url)

    # 添加二维码到库存信息
    inventory.qr_code = qr_code_base64

    return render_template('inventory/item_label_print.html', inventory=inventory)

# 库存统计分析功能
@inventory_bp.route('/inventory/statistics')
@login_required
@school_required
def statistics(user_area):
    """库存统计分析页面"""
    # 初始化默认值，确保变量始终存在
    warehouse = None
    storage_locations = []
    categories = []
    category_groups = {
        '肉类': [],
        '蔬菜': [],
        '调味品': [],
        '其他': []
    }
    category_groups_json = {
        '肉类': [],
        '蔬菜': [],
        '调味品': [],
        '其他': []
    }
    suppliers = []

    try:
        # 获取当前用户可访问的区域
        accessible_areas = current_user.get_accessible_areas()
        area_ids = [area.id for area in accessible_areas]

        # 获取当前学校的仓库（一个学校只有一个仓库）
        warehouse = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).first()

        # 获取该仓库的所有储存位置
        if warehouse:
            storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse.id, status='正常').all()

        # 获取食材分类 - 按主要分类组织
        from app.models import IngredientCategory
        categories = IngredientCategory.query.order_by(IngredientCategory.name).all()

        # 组织食材分类为主要类别（现在直接使用新的4个分类）
        for category in categories:
            try:
                category_name = category.name if category.name else ''
                # 直接按分类名称分组
                if category_name == '肉类':
                    category_groups['肉类'].append(category)
                elif category_name == '蔬菜':
                    category_groups['蔬菜'].append(category)
                elif category_name == '调味品':
                    category_groups['调味品'].append(category)
                else:
                    category_groups['其他'].append(category)
            except Exception as e:
                current_app.logger.error(f"处理食材分类时出错: {e}, category: {category}")
                category_groups['其他'].append(category)

        # 将category_groups转换为可JSON序列化的格式
        category_groups_json = {}
        for group_name, group_categories in category_groups.items():
            category_groups_json[group_name] = [
                {'id': cat.id, 'name': cat.name} for cat in group_categories
            ]

        # 获取与当前学校有关联的供应商
        # 注意：无论是管理员还是普通用户，都只显示与当前学校有合作关系的供应商
        from app.models import Supplier, SupplierSchoolRelation
        try:
            # 通过供应商-学校关联表筛选供应商
            # 只显示与当前用户可访问区域有合作关系且状态正常的供应商
            suppliers = Supplier.query.join(SupplierSchoolRelation)\
                        .filter(SupplierSchoolRelation.area_id.in_(area_ids))\
                        .filter(Supplier.status == 1)\
                        .filter(SupplierSchoolRelation.status == 1)\
                        .distinct()\
                        .order_by(Supplier.name).all()

            current_app.logger.info(f"库存统计页面：为区域 {area_ids} 找到 {len(suppliers)} 个关联供应商")
        except Exception as e:
            current_app.logger.error(f"获取供应商列表时出错: {e}")
            suppliers = []

    except Exception as e:
        current_app.logger.error(f"库存统计页面加载失败: {e}")
        flash(f'页面加载失败: {str(e)}', 'danger')

    # 无论是否出错，都返回模板（使用默认值）
    return render_template('inventory/statistics.html',
                          warehouse=warehouse,
                          storage_locations=storage_locations,
                          categories=categories,
                          category_groups=category_groups,
                          category_groups_json=category_groups_json,
                          suppliers=suppliers)

@inventory_bp.route('/inventory/statistics/data')
@login_required
@school_required
def statistics_data(user_area):
    """库存统计数据API"""
    # 获取查询参数
    stat_type = request.args.get('type', 'time')  # time, ingredient, supplier
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    storage_location_id = safe_get_int_param('storage_location_id')
    category_id = safe_get_int_param('category_id')
    supplier_id = safe_get_int_param('supplier_id')

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    try:
        if stat_type == 'ingredient':
            data = get_ingredient_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id)
        elif stat_type == 'ingredient_category':
            data = get_ingredient_category_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id)
        elif stat_type == 'supplier':
            data = get_supplier_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id)
        else:
            return jsonify({'success': False, 'message': '无效的统计类型'})

        return jsonify({'success': True, 'data': data})
    except Exception as e:
        current_app.logger.error(f"库存统计查询失败: {str(e)}")
        return jsonify({'success': False, 'message': f'查询失败: {str(e)}'})



def get_ingredient_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id):
    """按食材统计"""
    # 确保关键索引存在
    try:
        db.session.execute(text("""
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_date_warehouse')
            BEGIN
                CREATE NONCLUSTERED INDEX IX_stock_ins_date_warehouse
                ON stock_ins (stock_in_date, warehouse_id)
                INCLUDE (id, status)
            END
        """))

        db.session.execute(text("""
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_in_items_stock_in_ingredient')
            BEGIN
                CREATE NONCLUSTERED INDEX IX_stock_in_items_stock_in_ingredient
                ON stock_in_items (stock_in_id, ingredient_id)
                INCLUDE (quantity, unit_price, supplier_id, storage_location_id)
            END
        """))

        db.session.execute(text("""
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_outs_date_warehouse')
            BEGIN
                CREATE NONCLUSTERED INDEX IX_stock_outs_date_warehouse
                ON stock_outs (stock_out_date, warehouse_id)
                INCLUDE (id, status)
            END
        """))

        db.session.execute(text("""
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_out_items_stock_out_ingredient')
            BEGIN
                CREATE NONCLUSTERED INDEX IX_stock_out_items_stock_out_ingredient
                ON stock_out_items (stock_out_id, ingredient_id)
                INCLUDE (quantity, unit)
            END
        """))

        db.session.commit()
    except Exception as e:
        current_app.logger.warning(f"创建索引失败，但继续执行查询: {str(e)}")
        db.session.rollback()

    # 构建基础查询条件
    where_conditions = []
    params = {}

    # 区域权限
    if len(area_ids) == 1:
        where_conditions.append(f"w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        where_conditions.append(f"w.area_id IN ({area_ids_str})")

    # 时间范围
    if start_date:
        where_conditions.append("CAST(si.stock_in_date AS DATE) >= :start_date")
        params['start_date'] = start_date
    if end_date:
        where_conditions.append("CAST(si.stock_in_date AS DATE) <= :end_date")
        params['end_date'] = end_date

    # 储存位置筛选
    if storage_location_id:
        where_conditions.append("sii.storage_location_id = :storage_location_id")
        params['storage_location_id'] = storage_location_id

    # 分类筛选
    if category_id:
        where_conditions.append("i.category_id = :category_id")
        params['category_id'] = category_id

    # 供应商筛选
    if supplier_id:
        where_conditions.append("sii.supplier_id = :supplier_id")
        params['supplier_id'] = supplier_id

    where_clause = " AND ".join(where_conditions)

    # 按食材统计入库SQL - 优化版本，减少类型转换
    ingredient_in_sql = text(f"""
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            ISNULL(ic.name, '其他') as category_name,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2))) as total_in_quantity,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as total_in_amount,
            AVG(ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as avg_unit_price
        FROM stock_ins si WITH (NOLOCK)
        INNER JOIN stock_in_items sii WITH (NOLOCK) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w WITH (NOLOCK) ON si.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON sii.ingredient_id = i.id
        LEFT JOIN ingredient_categories ic WITH (NOLOCK) ON i.category_id = ic.id
        WHERE {where_clause} AND si.status = '已入库'
        GROUP BY i.id, i.name, ic.name
        ORDER BY total_in_amount DESC
    """)

    # 按食材统计出库SQL - 优化版本
    ingredient_out_sql = text(f"""
        SELECT
            i.id as ingredient_id,
            i.name as ingredient_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            SUM(TRY_CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity,
            0 as total_out_amount
        FROM stock_outs so WITH (NOLOCK)
        INNER JOIN stock_out_items soi WITH (NOLOCK) ON so.id = soi.stock_out_id
        INNER JOIN warehouses w WITH (NOLOCK) ON so.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON soi.ingredient_id = i.id
        WHERE {where_clause.replace('si.', 'so.').replace('sii.', 'soi.').replace('stock_in_date', 'stock_out_date')} AND so.status = '已出库'
        GROUP BY i.id, i.name
        ORDER BY total_out_quantity DESC
    """)

    # 执行查询 - 添加超时和限制
    try:
        # 设置查询超时
        db.session.execute(text("SET LOCK_TIMEOUT 30000"))  # 30秒超时

        # 执行入库查询，限制结果数量
        ingredient_in_result = db.session.execute(
            text(str(ingredient_in_sql).replace("ORDER BY total_in_amount DESC", "ORDER BY total_in_amount DESC OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY")),
            params
        ).fetchall()

        # 执行出库查询，限制结果数量
        ingredient_out_result = db.session.execute(
            text(str(ingredient_out_sql).replace("ORDER BY total_out_quantity DESC", "ORDER BY total_out_quantity DESC OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY")),
            params
        ).fetchall()

    except Exception as e:
        current_app.logger.error(f"库存统计查询执行失败: {str(e)}")
        # 如果查询失败，返回空结果而不是抛出异常
        return []

    # 合并入库出库数据
    ingredient_data = {}

    # 处理入库数据
    for row in ingredient_in_result:
        ingredient_id = row.ingredient_id
        ingredient_data[ingredient_id] = {
            'ingredient_id': ingredient_id,
            'ingredient_name': row.ingredient_name,
            'category_name': row.category_name or '未分类',
            'stock_in_count': row.stock_in_count or 0,
            'stock_in_items': row.stock_in_items or 0,
            'total_in_quantity': float(row.total_in_quantity or 0),
            'total_in_amount': float(row.total_in_amount or 0),
            'avg_unit_price': float(row.avg_unit_price or 0),

            'stock_out_count': 0,
            'stock_out_items': 0,
            'total_out_quantity': 0,
            'total_out_amount': 0
        }

    # 处理出库数据
    for row in ingredient_out_result:
        ingredient_id = row.ingredient_id
        if ingredient_id in ingredient_data:
            ingredient_data[ingredient_id].update({
                'stock_out_count': row.stock_out_count or 0,
                'stock_out_items': row.stock_out_items or 0,
                'total_out_quantity': float(row.total_out_quantity or 0),
                'total_out_amount': float(row.total_out_amount or 0)
            })
        else:
            # 如果只有出库记录而没有入库记录，也要包含在结果中
            ingredient_data[ingredient_id] = {
                'ingredient_id': ingredient_id,
                'ingredient_name': row.ingredient_name,
                'category_name': '未分类',
                'stock_in_count': 0,
                'stock_in_items': 0,
                'total_in_quantity': 0,
                'total_in_amount': 0,
                'avg_unit_price': 0,
                'stock_out_count': row.stock_out_count or 0,
                'stock_out_items': row.stock_out_items or 0,
                'total_out_quantity': float(row.total_out_quantity or 0),
                'total_out_amount': float(row.total_out_amount or 0)
            }

    # 计算周转率和库存余额
    for ingredient_id, data in ingredient_data.items():
        data['net_quantity'] = data['total_in_quantity'] - data['total_out_quantity']
        data['net_amount'] = data['total_in_amount'] - data['total_out_amount']
        data['turnover_rate'] = round(data['total_out_quantity'] / data['total_in_quantity'] * 100, 2) if data['total_in_quantity'] > 0 else 0

    return list(ingredient_data.values())

def get_ingredient_category_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id):
    """按食材分类统计汇总 - 优化版本"""
    # 构建基础查询条件
    where_conditions = []
    params = {}

    # 设置查询超时
    try:
        db.session.execute(text("SET LOCK_TIMEOUT 30000"))
    except:
        pass

    # 区域权限
    if len(area_ids) == 1:
        where_conditions.append(f"w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        where_conditions.append(f"w.area_id IN ({area_ids_str})")

    # 时间范围
    if start_date:
        where_conditions.append("CAST(si.stock_in_date AS DATE) >= :start_date")
        params['start_date'] = start_date
    if end_date:
        where_conditions.append("CAST(si.stock_in_date AS DATE) <= :end_date")
        params['end_date'] = end_date

    # 储存位置筛选
    if storage_location_id:
        where_conditions.append("sii.storage_location_id = :storage_location_id")
        params['storage_location_id'] = storage_location_id

    # 分类筛选
    if category_id:
        where_conditions.append("i.category_id = :category_id")
        params['category_id'] = category_id

    # 供应商筛选
    if supplier_id:
        where_conditions.append("sii.supplier_id = :supplier_id")
        params['supplier_id'] = supplier_id

    where_clause = " AND ".join(where_conditions)

    # 按分类统计入库SQL
    category_in_sql = text(f"""
        SELECT
            ISNULL(ic.name, '其他') as category_name,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            COUNT(DISTINCT i.id) as ingredient_count,
            SUM(CAST(sii.quantity AS DECIMAL(18,2))) as total_in_quantity,
            SUM(CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as total_in_amount,
            AVG(ISNULL(CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as avg_unit_price
        FROM stock_ins si
        JOIN stock_in_items sii ON si.id = sii.stock_in_id
        JOIN warehouses w ON si.warehouse_id = w.id
        JOIN ingredients i ON sii.ingredient_id = i.id
        LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
        WHERE {where_clause}
        GROUP BY ic.name
        ORDER BY total_in_amount DESC
    """)

    # 按分类统计出库SQL
    category_out_sql = text(f"""
        SELECT
            ISNULL(ic.name, '其他') as category_name,
            COUNT(DISTINCT so.id) as stock_out_count,
            COUNT(soi.id) as stock_out_items,
            COUNT(DISTINCT i.id) as ingredient_count,
            SUM(CAST(soi.quantity AS DECIMAL(18,2))) as total_out_quantity
        FROM stock_outs so
        JOIN stock_out_items soi ON so.id = soi.stock_out_id
        JOIN warehouses w ON so.warehouse_id = w.id
        JOIN ingredients i ON soi.ingredient_id = i.id
        LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
        WHERE {where_clause.replace('si.', 'so.').replace('sii.', 'soi.').replace('stock_in_date', 'stock_out_date')}
        GROUP BY ic.name
        ORDER BY total_out_quantity DESC
    """)

    # 执行查询
    category_in_result = db.session.execute(category_in_sql, params).fetchall()
    category_out_result = db.session.execute(category_out_sql, params).fetchall()

    # 合并入库出库数据
    category_data = {}

    # 处理入库数据
    for row in category_in_result:
        category_name = row.category_name or '其他'
        category_data[category_name] = {
            'category_name': category_name,
            'ingredient_count': row.ingredient_count or 0,
            'stock_in_count': row.stock_in_count or 0,
            'stock_in_items': row.stock_in_items or 0,
            'total_in_quantity': float(row.total_in_quantity or 0),
            'total_in_amount': float(row.total_in_amount or 0),
            'avg_unit_price': float(row.avg_unit_price or 0),
            'stock_out_count': 0,
            'stock_out_items': 0,
            'total_out_quantity': 0
        }

    # 处理出库数据
    for row in category_out_result:
        category_name = row.category_name or '其他'
        if category_name in category_data:
            category_data[category_name].update({
                'stock_out_count': row.stock_out_count or 0,
                'stock_out_items': row.stock_out_items or 0,
                'total_out_quantity': float(row.total_out_quantity or 0)
            })
        else:
            # 如果只有出库记录而没有入库记录
            category_data[category_name] = {
                'category_name': category_name,
                'ingredient_count': row.ingredient_count or 0,
                'stock_in_count': 0,
                'stock_in_items': 0,
                'total_in_quantity': 0,
                'total_in_amount': 0,
                'avg_unit_price': 0,
                'stock_out_count': row.stock_out_count or 0,
                'stock_out_items': row.stock_out_items or 0,
                'total_out_quantity': float(row.total_out_quantity or 0)
            }

    # 计算周转率、库存余量和成本占比
    total_amount = sum(data['total_in_amount'] for data in category_data.values())

    for category_name, data in category_data.items():
        data['net_quantity'] = data['total_in_quantity'] - data['total_out_quantity']
        data['turnover_rate'] = round(data['total_out_quantity'] / data['total_in_quantity'] * 100, 2) if data['total_in_quantity'] > 0 else 0
        data['cost_ratio'] = round(data['total_in_amount'] / total_amount * 100, 2) if total_amount > 0 else 0

    return list(category_data.values())

def get_supplier_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id):
    """按供应商统计 - 包含食材分析"""
    # 构建基础查询条件
    where_conditions = []
    params = {}

    # 设置查询超时
    try:
        db.session.execute(text("SET LOCK_TIMEOUT 30000"))
    except:
        pass

    # 区域权限
    if len(area_ids) == 1:
        where_conditions.append(f"w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        where_conditions.append(f"w.area_id IN ({area_ids_str})")

    # 时间范围
    if start_date:
        where_conditions.append("CAST(si.stock_in_date AS DATE) >= :start_date")
        params['start_date'] = start_date
    if end_date:
        where_conditions.append("CAST(si.stock_in_date AS DATE) <= :end_date")
        params['end_date'] = end_date

    # 储存位置筛选
    if storage_location_id:
        where_conditions.append("sii.storage_location_id = :storage_location_id")
        params['storage_location_id'] = storage_location_id

    # 分类筛选
    if category_id:
        where_conditions.append("i.category_id = :category_id")
        params['category_id'] = category_id

    # 供应商筛选
    if supplier_id:
        where_conditions.append("sii.supplier_id = :supplier_id")
        params['supplier_id'] = supplier_id

    where_clause = " AND ".join(where_conditions)

    # 按供应商基本统计SQL
    supplier_basic_sql = text(f"""
        SELECT
            s.id as supplier_id,
            s.name as supplier_name,
            s.contact_person,
            s.phone,
            s.address,
            COUNT(DISTINCT si.id) as stock_in_count,
            COUNT(sii.id) as stock_in_items,
            COUNT(DISTINCT i.id) as ingredient_types,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2))) as total_quantity,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as total_amount,
            AVG(ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as avg_unit_price
        FROM stock_ins si WITH (NOLOCK)
        INNER JOIN stock_in_items sii WITH (NOLOCK) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w WITH (NOLOCK) ON si.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON sii.ingredient_id = i.id
        INNER JOIN suppliers s WITH (NOLOCK) ON sii.supplier_id = s.id
        WHERE {where_clause} AND si.status = '已入库'
        GROUP BY s.id, s.name, s.contact_person, s.phone, s.address
        ORDER BY total_amount DESC
    """)

    # 按供应商的食材详细统计SQL
    supplier_ingredient_sql = text(f"""
        SELECT
            s.id as supplier_id,
            s.name as supplier_name,
            i.id as ingredient_id,
            i.name as ingredient_name,
            ISNULL(ic.name, '其他') as category_name,
            COUNT(DISTINCT si.id) as ingredient_stock_in_count,
            COUNT(sii.id) as ingredient_stock_in_items,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2))) as ingredient_total_quantity,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as ingredient_total_amount,
            AVG(ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as ingredient_avg_price
        FROM stock_ins si WITH (NOLOCK)
        INNER JOIN stock_in_items sii WITH (NOLOCK) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w WITH (NOLOCK) ON si.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON sii.ingredient_id = i.id
        INNER JOIN suppliers s WITH (NOLOCK) ON sii.supplier_id = s.id
        LEFT JOIN ingredient_categories ic WITH (NOLOCK) ON i.category_id = ic.id
        WHERE {where_clause} AND si.status = '已入库'
        GROUP BY s.id, s.name, i.id, i.name, ic.name
        ORDER BY s.id, ingredient_total_amount DESC
    """)

    # 按供应商的食材分类汇总SQL
    supplier_category_sql = text(f"""
        SELECT
            s.id as supplier_id,
            s.name as supplier_name,
            ISNULL(ic.name, '其他') as category_name,
            COUNT(DISTINCT i.id) as category_ingredient_count,
            COUNT(DISTINCT si.id) as category_stock_in_count,
            COUNT(sii.id) as category_stock_in_items,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2))) as category_total_quantity,
            SUM(TRY_CAST(sii.quantity AS DECIMAL(18,2)) * ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as category_total_amount,
            AVG(ISNULL(TRY_CAST(sii.unit_price AS DECIMAL(18,2)), 0)) as category_avg_price
        FROM stock_ins si WITH (NOLOCK)
        INNER JOIN stock_in_items sii WITH (NOLOCK) ON si.id = sii.stock_in_id
        INNER JOIN warehouses w WITH (NOLOCK) ON si.warehouse_id = w.id
        INNER JOIN ingredients i WITH (NOLOCK) ON sii.ingredient_id = i.id
        INNER JOIN suppliers s WITH (NOLOCK) ON sii.supplier_id = s.id
        LEFT JOIN ingredient_categories ic WITH (NOLOCK) ON i.category_id = ic.id
        WHERE {where_clause} AND si.status = '已入库'
        GROUP BY s.id, s.name, ic.name
        ORDER BY s.id, category_total_amount DESC
    """)

    # 执行查询
    try:
        # 执行基本统计查询
        supplier_basic_result = db.session.execute(supplier_basic_sql, params).fetchall()

        # 执行食材详细统计查询
        supplier_ingredient_result = db.session.execute(supplier_ingredient_sql, params).fetchall()

        # 执行食材分类汇总查询
        supplier_category_result = db.session.execute(supplier_category_sql, params).fetchall()

    except Exception as e:
        current_app.logger.error(f"供应商统计查询执行失败: {str(e)}")
        return []

    # 处理数据 - 构建供应商数据结构
    supplier_data = {}

    # 处理基本统计数据
    for row in supplier_basic_result:
        supplier_id = row.supplier_id
        supplier_data[supplier_id] = {
            'supplier_id': supplier_id,
            'supplier_name': row.supplier_name,
            'contact_person': row.contact_person or '',
            'phone': row.phone or '',
            'address': row.address or '',
            'stock_in_count': row.stock_in_count or 0,
            'stock_in_items': row.stock_in_items or 0,
            'ingredient_types': row.ingredient_types or 0,
            'total_quantity': float(row.total_quantity or 0),
            'total_amount': float(row.total_amount or 0),
            'avg_unit_price': float(row.avg_unit_price or 0),
            'ingredients': [],  # 该供应商的食材详细列表
            'categories': {},   # 该供应商的食材分类汇总
            'top_ingredients': []  # 主要供应食材TOP5
        }

    # 处理食材详细统计数据
    for row in supplier_ingredient_result:
        supplier_id = row.supplier_id
        if supplier_id in supplier_data:
            ingredient_data = {
                'ingredient_id': row.ingredient_id,
                'ingredient_name': row.ingredient_name,
                'category_name': row.category_name or '其他',
                'stock_in_count': row.ingredient_stock_in_count or 0,
                'stock_in_items': row.ingredient_stock_in_items or 0,
                'total_quantity': float(row.ingredient_total_quantity or 0),
                'total_amount': float(row.ingredient_total_amount or 0),
                'avg_price': float(row.ingredient_avg_price or 0)
            }
            supplier_data[supplier_id]['ingredients'].append(ingredient_data)

    # 处理食材分类汇总数据
    for row in supplier_category_result:
        supplier_id = row.supplier_id
        if supplier_id in supplier_data:
            category_name = row.category_name or '其他'
            supplier_data[supplier_id]['categories'][category_name] = {
                'category_name': category_name,
                'ingredient_count': row.category_ingredient_count or 0,
                'stock_in_count': row.category_stock_in_count or 0,
                'stock_in_items': row.category_stock_in_items or 0,
                'total_quantity': float(row.category_total_quantity or 0),
                'total_amount': float(row.category_total_amount or 0),
                'avg_price': float(row.category_avg_price or 0)
            }

    # 为每个供应商计算TOP食材和分类占比
    for supplier_id, data in supplier_data.items():
        # 计算TOP5主要供应食材
        data['top_ingredients'] = sorted(data['ingredients'],
                                       key=lambda x: x['total_amount'],
                                       reverse=True)[:5]

        # 计算各分类在该供应商中的占比
        total_supplier_amount = data['total_amount']
        for category_name, category_data in data['categories'].items():
            category_data['amount_ratio'] = round(
                category_data['total_amount'] / total_supplier_amount * 100, 2
            ) if total_supplier_amount > 0 else 0

    return list(supplier_data.values())

# 库存统计报表打印
@inventory_bp.route('/inventory/statistics/print')
@login_required
@school_required
def print_statistics(user_area):
    """打印库存统计报表"""
    # 获取查询参数
    stat_type = request.args.get('type', 'time')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    storage_location_id = safe_get_int_param('storage_location_id')
    category_id = safe_get_int_param('category_id')
    supplier_id = safe_get_int_param('supplier_id')

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    try:
        if stat_type == 'ingredient':
            data = get_ingredient_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id)
        elif stat_type == 'ingredient_category':
            data = get_ingredient_category_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id)
        elif stat_type == 'supplier':
            data = get_supplier_statistics(area_ids, start_date, end_date, storage_location_id, category_id, supplier_id)
        else:
            flash('无效的统计类型', 'danger')
            return redirect(url_for('inventory.statistics'))

        # 获取筛选信息
        from app.models import IngredientCategory, Supplier
        filter_info = {
            'type': stat_type,
            'start_date': start_date,
            'end_date': end_date,
            'storage_location': StorageLocation.query.get(storage_location_id).name if storage_location_id else '全部位置',
            'category': IngredientCategory.query.get(category_id).name if category_id else '全部分类',
            'supplier': Supplier.query.get(supplier_id).name if supplier_id else '全部供应商'
        }

        return render_template('inventory/statistics_print.html',
                              stat_type=stat_type,
                              data=data,
                              filter_info=filter_info,
                              print_date=datetime.now())

    except Exception as e:
        current_app.logger.error(f"库存统计打印失败: {str(e)}")
        flash(f'统计打印失败: {str(e)}', 'danger')
        return redirect(url_for('inventory.statistics'))
