{% extends "financial/base.html" %}

{% block title %}成本分析报表{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item">成本分析报表</span>
{% endblock %}

{% block page_title %}成本分析报表{% endblock %}

{% block page_actions %}
<button type="button" class="uf-btn uf-btn-primary" onclick="exportReport()">
    <i class="fas fa-download uf-icon"></i> 导出报表
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格查询条件区域 - 一行显示 -->
<div class="uf-card">
    <div class="uf-card-header">
        <h3>查询条件</h3>
    </div>
    <div class="uf-card-body">
        <form method="GET" class="uf-form" style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
            <div class="uf-form-group" style="min-width: 140px;">
                <label class="uf-form-label">开始日期</label>
                <input type="date" name="start_date" value="{{ start_date }}" class="uf-form-control">
            </div>
            <div class="uf-form-group" style="min-width: 140px;">
                <label class="uf-form-label">结束日期</label>
                <input type="date" name="end_date" value="{{ end_date }}" class="uf-form-control">
            </div>
            <div class="uf-form-group" style="min-width: 120px;">
                <label class="uf-form-label">分析类型</label>
                <select name="analysis_type" class="uf-form-control">
                    <option value="monthly" {% if analysis_type == 'monthly' %}selected{% endif %}>按月分析</option>
                    <option value="daily" {% if analysis_type == 'daily' %}selected{% endif %}>按日分析</option>
                    <option value="ingredient" {% if analysis_type == 'ingredient' %}selected{% endif %}>按食材分析</option>
                </select>
            </div>
            <div class="uf-form-group">
                <button type="submit" class="uf-btn uf-btn-primary">查询</button>
                <button type="button" class="uf-btn uf-btn-secondary" onclick="resetForm()">重置</button>
            </div>
        </form>
    </div>
</div>

<!-- 成本分析汇总 -->
<div class="uf-card">
    <div class="uf-card-header">
        <h3>成本分析汇总</h3>
        <div class="uf-card-tools">
            <span class="uf-text-muted">分析期间：{{ start_date }} 至 {{ end_date }}</span>
        </div>
    </div>
    <div class="uf-card-body">
        <div class="uf-row">
            <div class="uf-col-md-3">
                <div class="uf-stat-card">
                    <div class="uf-stat-icon" style="background-color: #e3f2fd;">
                        <i class="fas fa-chart-line" style="color: #1976d2;"></i>
                    </div>
                    <div class="uf-stat-content">
                        <div class="uf-stat-title">总成本</div>
                        <div class="uf-stat-value">{{ "%.2f"|format(cost_analysis_data.total_cost) }}</div>
                    </div>
                </div>
            </div>
            <div class="uf-col-md-3">
                <div class="uf-stat-card">
                    <div class="uf-stat-icon" style="background-color: #e8f5e8;">
                        <i class="fas fa-calculator" style="color: #388e3c;"></i>
                    </div>
                    <div class="uf-stat-content">
                        <div class="uf-stat-title">记录数量</div>
                        <div class="uf-stat-value">{{ cost_analysis_data.cost_data|length }}</div>
                    </div>
                </div>
            </div>
            <div class="uf-col-md-3">
                <div class="uf-stat-card">
                    <div class="uf-stat-icon" style="background-color: #fff3e0;">
                        <i class="fas fa-chart-bar" style="color: #f57c00;"></i>
                    </div>
                    <div class="uf-stat-content">
                        <div class="uf-stat-title">平均成本</div>
                        <div class="uf-stat-value">
                            {% if cost_analysis_data.cost_data|length > 0 %}
                                {{ "%.2f"|format(cost_analysis_data.total_cost / cost_analysis_data.cost_data|length) }}
                            {% else %}
                                0.00
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="uf-col-md-3">
                <div class="uf-stat-card">
                    <div class="uf-stat-icon" style="background-color: #fce4ec;">
                        <i class="fas fa-percentage" style="color: #c2185b;"></i>
                    </div>
                    <div class="uf-stat-content">
                        <div class="uf-stat-title">分析类型</div>
                        <div class="uf-stat-value" style="font-size: 14px;">
                            {% if analysis_type == 'monthly' %}按月分析
                            {% elif analysis_type == 'daily' %}按日分析
                            {% else %}按食材分析{% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成本分析明细 -->
<div class="uf-card">
    <div class="uf-card-header">
        <h3>成本分析明细</h3>
        <div class="uf-card-tools">
            <span class="uf-text-muted">共 {{ cost_analysis_data.cost_data|length }} 条记录</span>
        </div>
    </div>
    <div class="uf-card-body">
        <div class="uf-table-container">
            <table class="uf-table">
                <thead>
                    <tr>
                        {% if analysis_type == 'monthly' or analysis_type == 'daily' %}
                        <th width="150">期间</th>
                        <th width="120">总成本</th>
                        <th width="100">入库次数</th>
                        <th width="120">平均成本</th>
                        {% else %}
                        <th width="200">食材名称</th>
                        <th width="120">食材类别</th>
                        <th width="100">总数量</th>
                        <th width="120">总成本</th>
                        <th width="100">平均单价</th>
                        <th width="100">入库次数</th>
                        {% endif %}
                        <th width="80">占比</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in cost_analysis_data.cost_data %}
                    <tr>
                        {% if analysis_type == 'monthly' or analysis_type == 'daily' %}
                        <td>{{ item.period }}</td>
                        <td class="uf-text-right">{{ "%.2f"|format(item.total_cost) }}</td>
                        <td class="uf-text-center">{{ item.stock_in_count }}</td>
                        <td class="uf-text-right">{{ "%.2f"|format(item.avg_cost) }}</td>
                        {% else %}
                        <td>{{ item.ingredient_name }}</td>
                        <td>{{ item.category or '未分类' }}</td>
                        <td class="uf-text-right">{{ "%.2f"|format(item.total_quantity) }}</td>
                        <td class="uf-text-right">{{ "%.2f"|format(item.total_cost) }}</td>
                        <td class="uf-text-right">{{ "%.2f"|format(item.avg_unit_cost) }}</td>
                        <td class="uf-text-center">{{ item.stock_in_count }}</td>
                        {% endif %}
                        <td class="uf-text-right">
                            {% if cost_analysis_data.total_cost > 0 %}
                                {{ "%.1f"|format(item.total_cost / cost_analysis_data.total_cost * 100) }}%
                            {% else %}
                                0.0%
                            {% endif %}
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="{% if analysis_type == 'ingredient' %}7{% else %}5{% endif %}" class="uf-text-center uf-text-muted">
                            暂无数据
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 成本趋势图表 -->
{% if cost_analysis_data.cost_data|length > 0 and analysis_type != 'ingredient' %}
<div class="uf-card">
    <div class="uf-card-header">
        <h3>成本趋势图</h3>
    </div>
    <div class="uf-card-body">
        <canvas id="costChart" width="400" height="200"></canvas>
    </div>
</div>
{% endif %}

<script>
function resetForm() {
    const today = new Date().toISOString().split('T')[0];
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    
    document.querySelector('input[name="start_date"]').value = firstDay;
    document.querySelector('input[name="end_date"]').value = today;
    document.querySelector('select[name="analysis_type"]').value = 'monthly';
}

function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`${window.location.pathname}?${params.toString()}`);
}

// 绘制成本趋势图
{% if cost_analysis_data.cost_data|length > 0 and analysis_type != 'ingredient' %}
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('costChart').getContext('2d');
    const data = {{ cost_analysis_data.cost_data | tojson }};
    
    const labels = data.map(item => item.period);
    const costs = data.map(item => item.total_cost);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '成本金额',
                data: costs,
                borderColor: '#1976d2',
                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '成本: ¥' + context.parsed.y.toFixed(2);
                        }
                    }
                }
            }
        }
    });
});
{% endif %}
</script>

<!-- 引入Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}
