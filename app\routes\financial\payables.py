"""
应付账款管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db, csrf
from app.routes.financial import financial_bp
from app.models_financial import AccountPayable, FinancialVoucher, VoucherDetail, AccountingSubject
from app.models import StockIn, Supplier
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text
from datetime import datetime, date


@financial_bp.route('/payables')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payables_index(user_area):
    """应付账款列表"""

    # 获取搜索参数 - 将查询条件区域一行显示
    keyword = request.args.get('keyword', '').strip()
    supplier_id = request.args.get('supplier_id', type=int)
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()

    # 使用原生SQL构建高效查询
    base_sql = """
        SELECT ap.*, s.name as supplier_name, si.stock_in_number, si.stock_in_date,
               u.username as creator_name, po.order_number
        FROM account_payables ap
        LEFT JOIN suppliers s ON ap.supplier_id = s.id
        LEFT JOIN stock_ins si ON ap.stock_in_id = si.id
        LEFT JOIN users u ON ap.created_by = u.id
        LEFT JOIN purchase_orders po ON ap.purchase_order_id = po.id
        WHERE ap.area_id = :area_id
    """

    params = {'area_id': user_area.id}
    conditions = []
    
    if keyword:
        conditions.append("(ap.payable_number LIKE :keyword OR ap.invoice_number LIKE :keyword OR s.name LIKE :keyword)")
        params['keyword'] = f'%{keyword}%'

    if supplier_id:
        conditions.append("ap.supplier_id = :supplier_id")
        params['supplier_id'] = supplier_id

    if status:
        conditions.append("ap.status = :status")
        params['status'] = status

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            conditions.append("CAST(ap.created_at AS DATE) >= :start_date")
            params['start_date'] = start_date_obj
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            conditions.append("CAST(ap.created_at AS DATE) <= :end_date")
            params['end_date'] = end_date_obj
        except ValueError:
            pass

    # 构建完整SQL
    if conditions:
        base_sql += " AND " + " AND ".join(conditions)

    # 分页处理
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    offset = (page - 1) * per_page

    # 获取总数 - 不使用ORDER BY的子查询
    count_sql = f"SELECT COUNT(*) FROM ({base_sql}) as count_query"
    total = db.session.execute(text(count_sql), params).scalar()

    # 获取分页数据 - 在外层添加ORDER BY
    paginated_sql = f"{base_sql} ORDER BY ap.created_at DESC OFFSET {offset} ROWS FETCH NEXT {per_page} ROWS ONLY"
    payables_data = db.session.execute(text(paginated_sql), params).fetchall()
    
    # 获取供应商列表用于筛选
    supplier_sql = text("""
        SELECT DISTINCT s.id, s.name
        FROM suppliers s
        INNER JOIN account_payables ap ON s.id = ap.supplier_id
        WHERE ap.area_id = :area_id
        ORDER BY s.name
    """)
    suppliers = db.session.execute(supplier_sql, {'area_id': user_area.id}).fetchall()

    # 构建分页对象
    class PaginationMock:
        def __init__(self, items, total, page, per_page):
            self.items = items
            self.total = total
            self.page = page
            self.per_page = per_page
            self.pages = (total + per_page - 1) // per_page
            self.has_prev = page > 1
            self.has_next = page < self.pages
            self.prev_num = page - 1 if self.has_prev else None
            self.next_num = page + 1 if self.has_next else None

        def __len__(self):
            return len(self.items)

        def __iter__(self):
            return iter(self.items)

        def iter_pages(self, left_edge=2, left_current=2, right_current=5, right_edge=2):
            last = 0
            for num in range(1, self.pages + 1):
                if num <= left_edge or \
                   (num > self.page - left_current - 1 and num < self.page + right_current) or \
                   num > self.pages - right_edge:
                    if last + 1 != num:
                        yield None
                    yield num
                    last = num

    payables = PaginationMock(payables_data, total, page, per_page)

    return render_template('financial/payables/index.html',
                         payables=payables,
                         suppliers=suppliers,
                         keyword=keyword,
                         supplier_id=supplier_id,
                         status=status,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/payables/<int:id>')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def view_payable(id):
    """查看应付账款详情"""
    user_area = current_user.get_current_area()
    payable = AccountPayable.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 获取付款记录
    from app.models_financial import PaymentRecord
    payments = PaymentRecord.query.filter_by(payable_id=id).order_by(PaymentRecord.payment_date.desc()).all()
    
    return render_template('financial/payables/view.html',
                         payable=payable, payments=payments)


@financial_bp.route('/payables/pending-stock-ins')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payables_pending_stock_ins(user_area):
    """待处理入库单 - 生成应付账款"""

    # 获取搜索参数 - 将查询条件区域一行显示
    keyword = request.args.get('keyword', '').strip()
    supplier_id = request.args.get('supplier_id', type=int)
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()

    # 使用原生SQL查询待处理的入库单
    base_sql = """
        SELECT si.id, si.stock_in_number, si.stock_in_date, si.supplier_id, si.total_cost,
               si.status, si.operator_id, si.created_at, si.warehouse_id, si.purchase_order_id,
               s.name as supplier_name, u.username as operator_name,
               COUNT(sii.id) as item_count,
               CASE WHEN ap.id IS NOT NULL THEN 1 ELSE 0 END as has_payable
        FROM stock_ins si
        LEFT JOIN suppliers s ON si.supplier_id = s.id
        LEFT JOIN users u ON si.operator_id = u.id
        LEFT JOIN stock_in_items sii ON si.id = sii.stock_in_id
        LEFT JOIN account_payables ap ON si.id = ap.stock_in_id
        WHERE si.area_id = :area_id
        AND si.status = '已确认'
        AND si.is_financial_confirmed = 0
    """

    params = {'area_id': user_area.id}
    conditions = []

    if keyword:
        conditions.append("(si.stock_in_number LIKE :keyword OR s.name LIKE :keyword)")
        params['keyword'] = f'%{keyword}%'

    if supplier_id:
        conditions.append("si.supplier_id = :supplier_id")
        params['supplier_id'] = supplier_id

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            conditions.append("si.stock_in_date >= :start_date")
            params['start_date'] = start_date_obj
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            conditions.append("si.stock_in_date <= :end_date")
            params['end_date'] = end_date_obj
        except ValueError:
            pass

    # 构建完整SQL
    if conditions:
        base_sql += " AND " + " AND ".join(conditions)

    base_sql += """
        GROUP BY si.id, si.stock_in_number, si.stock_in_date, si.supplier_id, si.total_cost,
                 si.status, si.operator_id, si.created_at, si.warehouse_id, si.purchase_order_id,
                 s.name, u.username, ap.id
    """

    # 分页处理
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    offset = (page - 1) * per_page

    # 获取总数 - 使用简化的计数查询，不包含聚合函数
    count_sql = """
        SELECT COUNT(DISTINCT si.id)
        FROM stock_ins si
        LEFT JOIN suppliers s ON si.supplier_id = s.id
        LEFT JOIN account_payables ap ON si.id = ap.stock_in_id
        WHERE si.area_id = :area_id
        AND si.status = '已确认'
        AND si.is_financial_confirmed = 0
    """

    # 添加相同的筛选条件
    if conditions:
        count_sql += " AND " + " AND ".join(conditions)

    total = db.session.execute(text(count_sql), params).scalar()

    # 获取分页数据 - 在外层添加ORDER BY
    paginated_sql = f"{base_sql} ORDER BY si.stock_in_date DESC, si.created_at DESC OFFSET {offset} ROWS FETCH NEXT {per_page} ROWS ONLY"
    stock_ins_data = db.session.execute(text(paginated_sql), params).fetchall()

    # 获取供应商列表用于筛选
    supplier_sql = text("""
        SELECT DISTINCT s.id, s.name
        FROM suppliers s
        INNER JOIN stock_ins si ON s.id = si.supplier_id
        WHERE si.area_id = :area_id AND si.status = '已确认' AND si.is_financial_confirmed = 0
        ORDER BY s.name
    """)
    suppliers = db.session.execute(supplier_sql, {'area_id': user_area.id}).fetchall()

    # 构建分页对象
    class PaginationMock:
        def __init__(self, items, total, page, per_page):
            self.items = items
            self.total = total
            self.page = page
            self.per_page = per_page
            self.pages = (total + per_page - 1) // per_page
            self.has_prev = page > 1
            self.has_next = page < self.pages
            self.prev_num = page - 1 if self.has_prev else None
            self.next_num = page + 1 if self.has_next else None

        def __len__(self):
            return len(self.items)

        def __iter__(self):
            return iter(self.items)

        def iter_pages(self, left_edge=2, left_current=2, right_current=5, right_edge=2):
            last = 0
            for num in range(1, self.pages + 1):
                if num <= left_edge or \
                   (num > self.page - left_current - 1 and num < self.page + right_current) or \
                   num > self.pages - right_edge:
                    if last + 1 != num:
                        yield None
                    yield num
                    last = num

    stock_ins = PaginationMock(stock_ins_data, total, page, per_page)

    return render_template('financial/payables/pending_stock_ins.html',
                         stock_ins=stock_ins,
                         suppliers=suppliers,
                         keyword=keyword,
                         supplier_id=supplier_id,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/payables/generate-from-stock-in', methods=['POST'])
@csrf.exempt
@login_required
@school_required
@check_permission('应付账款管理', 'create')
def generate_payable_from_stock_in(user_area):
    """从入库单生成应付账款"""
    current_app.logger.info("=== 开始生成应付账款 ===")

    # 获取请求数据
    request_data = request.get_json()
    current_app.logger.info(f"请求数据: {request_data}")

    stock_in_id = request_data.get('stock_in_id') if request_data else None
    current_app.logger.info(f"入库单ID: {stock_in_id}")

    if not stock_in_id:
        current_app.logger.error("未提供入库单ID")
        return jsonify({'success': False, 'message': '请选择入库单'})
    
    # 检查入库单是否存在且属于当前学校
    current_app.logger.info(f"查询入库单: ID={stock_in_id}, area_id={user_area.id}")
    stock_in = StockIn.query.filter_by(
        id=stock_in_id,
        area_id=user_area.id
    ).first()

    if not stock_in:
        current_app.logger.error(f"入库单不存在: ID={stock_in_id}")
        return jsonify({'success': False, 'message': '入库单不存在'})

    current_app.logger.info(f"找到入库单: {stock_in.stock_in_number}, 状态: {stock_in.status}, 总金额: {stock_in.total_cost}")
    
    # 检查是否已生成应付账款
    current_app.logger.info("检查是否已生成应付账款...")
    existing = AccountPayable.query.filter_by(stock_in_id=stock_in_id).first()
    if existing:
        current_app.logger.error(f"该入库单已生成应付账款: {existing.payable_number}")
        return jsonify({'success': False, 'message': '该入库单已生成应付账款'})

    # 检查入库单是否已财务确认
    current_app.logger.info(f"检查入库单状态: {stock_in.status}")
    if stock_in.status != '已入库':
        current_app.logger.error(f"入库单状态不正确: {stock_in.status}")
        return jsonify({'success': False, 'message': '入库单尚未入库'})
    
    try:
        current_app.logger.info("开始生成应付账款...")

        # 生成应付账款编号
        today = date.today()
        prefix = f"AP{today.strftime('%Y%m%d')}"
        current_app.logger.info(f"应付账款编号前缀: {prefix}")

        last_payable = AccountPayable.query.filter(
            AccountPayable.area_id == user_area.id,
            AccountPayable.payable_number.like(f'{prefix}%')
        ).order_by(AccountPayable.payable_number.desc()).first()

        if last_payable:
            last_number = int(last_payable.payable_number[-3:])
            payable_number = f"{prefix}{last_number + 1:03d}"
            current_app.logger.info(f"基于最后编号 {last_payable.payable_number} 生成新编号: {payable_number}")
        else:
            payable_number = f"{prefix}001"
            current_app.logger.info(f"生成首个应付账款编号: {payable_number}")
        
        # 使用字符串格式化避免ODBC兼容性问题
        sql_payable = f"""
            INSERT INTO account_payables
            (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id,
             original_amount, paid_amount, balance_amount, due_date, status,
             payment_terms, invoice_number, invoice_date, invoice_amount,
             created_by, notes)
            VALUES
            ('{payable_number}', {user_area.id}, {stock_in.supplier_id or 'NULL'}, {stock_in_id}, {stock_in.purchase_order_id or 'NULL'},
             {float(stock_in.total_cost)}, 0.0, {float(stock_in.total_cost)}, NULL, '未付款',
             NULL, NULL, NULL, NULL,
             {current_user.id}, NULL)
        """

        # 生成财务凭证号
        voucher_prefix = f"PZ{today.strftime('%Y%m%d')}"
        last_voucher = FinancialVoucher.query.filter(
            FinancialVoucher.area_id == user_area.id,
            FinancialVoucher.voucher_number.like(f'{voucher_prefix}%')
        ).order_by(FinancialVoucher.voucher_number.desc()).first()

        if last_voucher:
            last_number = int(last_voucher.voucher_number[-3:])
            voucher_number = f"{voucher_prefix}{last_number + 1:03d}"
        else:
            voucher_number = f"{voucher_prefix}001"

        # 使用字符串格式化避免ODBC兼容性问题
        sql_voucher = f"""
            INSERT INTO financial_vouchers
            (voucher_number, voucher_date, area_id, voucher_type, summary,
             total_amount, status, source_type, source_id, created_by)
            VALUES
            ('{voucher_number}', '{today.strftime('%Y-%m-%d')}', {user_area.id}, '入库凭证', '入库单{stock_in.stock_in_number}生成应付账款',
             {float(stock_in.total_cost)}, '已审核', '入库单', {stock_in_id}, {current_user.id})
        """


        
        # 获取会计科目（简化查询逻辑）
        current_app.logger.info("开始查询会计科目...")

        current_app.logger.info("查询原材料科目 (1201)...")
        inventory_subject = AccountingSubject.query.filter(
            AccountingSubject.code == '1201',
            db.or_(
                AccountingSubject.is_system == 1,  # 系统科目
                AccountingSubject.area_id == user_area.id  # 学校科目
            )
        ).first()

        if inventory_subject:
            current_app.logger.info(f"找到原材料科目: {inventory_subject.name} (ID: {inventory_subject.id})")
        else:
            current_app.logger.error("未找到原材料科目 (1201)")

        current_app.logger.info("查询应付账款科目 (2001)...")
        payable_subject = AccountingSubject.query.filter(
            AccountingSubject.code == '2001',
            db.or_(
                AccountingSubject.is_system == 1,  # 系统科目
                AccountingSubject.area_id == user_area.id  # 学校科目
            )
        ).first()

        if payable_subject:
            current_app.logger.info(f"找到应付账款科目: {payable_subject.name} (ID: {payable_subject.id})")
        else:
            current_app.logger.error("未找到应付账款科目 (2001)")

        if not inventory_subject or not payable_subject:
            current_app.logger.error("会计科目缺失，无法继续")
            return jsonify({
                'success': False,
                'message': '缺少必要的会计科目，请联系管理员初始化系统科目'
            })

        # 准备凭证明细 SQL（使用字符串格式化）
        current_app.logger.info("准备凭证明细SQL...")

        # 准备更新入库单 SQL（使用字符串格式化）
        current_app.logger.info("准备更新入库单SQL...")

        # 使用简化的事务处理，避免ODBC兼容性问题
        current_app.logger.info("开始执行数据库事务...")

        try:
            # 1. 创建应付账款
            current_app.logger.info("执行应付账款SQL...")
            db.session.execute(text(sql_payable))
            db.session.flush()  # 刷新以获取ID

            # 获取刚创建的应付账款ID
            payable_result = db.session.execute(text(f"""
                SELECT id FROM account_payables
                WHERE payable_number = '{payable_number}' AND area_id = {user_area.id}
            """)).fetchone()
            payable_id = payable_result[0]
            current_app.logger.info(f"应付账款创建成功，ID: {payable_id}")

            # 2. 创建财务凭证
            current_app.logger.info("执行财务凭证SQL...")
            db.session.execute(text(sql_voucher))
            db.session.flush()  # 刷新以获取ID

            # 获取刚创建的财务凭证ID
            voucher_result = db.session.execute(text(f"""
                SELECT id FROM financial_vouchers
                WHERE voucher_number = '{voucher_number}' AND area_id = {user_area.id}
            """)).fetchone()
            voucher_id = voucher_result[0]
            current_app.logger.info(f"财务凭证创建成功，ID: {voucher_id}")

            # 3. 创建凭证明细
            if inventory_subject and payable_subject:
                current_app.logger.info("创建凭证明细...")

                # 借：原材料
                detail1_sql = f"""
                    INSERT INTO voucher_details
                    (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount)
                    VALUES
                    ({voucher_id}, 1, {inventory_subject.id}, '入库单{stock_in.stock_in_number}', {float(stock_in.total_cost)}, 0.0)
                """
                db.session.execute(text(detail1_sql))
                current_app.logger.info("借方明细创建成功")

                # 贷：应付账款
                detail2_sql = f"""
                    INSERT INTO voucher_details
                    (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount)
                    VALUES
                    ({voucher_id}, 2, {payable_subject.id}, '入库单{stock_in.stock_in_number}', 0.0, {float(stock_in.total_cost)})
                """
                db.session.execute(text(detail2_sql))
                current_app.logger.info("贷方明细创建成功")
            else:
                current_app.logger.error(f"会计科目缺失: inventory_subject={inventory_subject}, payable_subject={payable_subject}")

            # 4. 更新入库单关联信息
            current_app.logger.info("更新入库单关联信息...")
            update_sql = f"""
                UPDATE stock_ins
                SET payable_id = {payable_id}, voucher_id = {voucher_id}
                WHERE id = {stock_in_id}
            """
            db.session.execute(text(update_sql))
            current_app.logger.info("入库单更新成功")

            # 提交事务
            db.session.commit()
            current_app.logger.info("事务提交成功")

            current_app.logger.info(f"=== 应付账款生成成功 ===")
            current_app.logger.info(f"应付账款ID: {payable_id}")
            current_app.logger.info(f"财务凭证ID: {voucher_id}")

            return jsonify({
                'success': True,
                'message': '应付账款生成成功',
                'payable_id': payable_id
            })

        except Exception as inner_e:
            db.session.rollback()
            current_app.logger.error(f"数据库操作失败: {str(inner_e)}")
            raise inner_e
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"生成应付账款失败: {str(e)}")
        current_app.logger.error(f"错误类型: {type(e).__name__}")
        current_app.logger.error(f"入库单ID: {stock_in_id}")
        if 'stock_in' in locals():
            current_app.logger.error(f"入库单信息: {stock_in.stock_in_number}, 总金额: {stock_in.total_cost}, 类型: {type(stock_in.total_cost)}")
        return jsonify({'success': False, 'message': f'生成失败: {str(e)}'})





@financial_bp.route('/payables/api/summary')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payables_summary_api():
    """应付账款汇总API"""
    user_area = current_user.get_current_area()

    # 统计各状态的应付账款
    summary = db.session.query(
        AccountPayable.status,
        db.func.count(AccountPayable.id).label('count'),
        db.func.sum(AccountPayable.balance_amount).label('total_amount')
    ).filter_by(area_id=user_area.id).group_by(AccountPayable.status).all()

    result = {}
    for status, count, total_amount in summary:
        result[status] = {
            'count': count,
            'total_amount': float(total_amount) if total_amount else 0
        }

    return jsonify(result)
