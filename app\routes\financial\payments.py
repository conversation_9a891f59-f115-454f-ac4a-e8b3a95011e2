"""
付款记录管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import PaymentRecord, AccountPayable, FinancialVoucher, VoucherDetail, AccountingSubject
from app.forms.financial import PaymentRecordForm
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text
from datetime import datetime, date


@financial_bp.route('/payments')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payments_index(user_area):
    """付款记录列表"""

    # 获取搜索参数 - 将查询条件区域一行显示
    keyword = request.args.get('keyword', '').strip()
    supplier_id = request.args.get('supplier_id', type=int)
    payment_method = request.args.get('payment_method', '').strip()
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()

    # 使用原生SQL构建高效查询，避免ORM性能问题
    base_sql = """
        SELECT pr.*, s.name as supplier_name, ap.payable_number, ap.original_amount as payable_amount,
               u1.username as creator_name, u2.username as reviewer_name
        FROM payment_records pr
        LEFT JOIN suppliers s ON pr.supplier_id = s.id
        LEFT JOIN account_payables ap ON pr.payable_id = ap.id
        LEFT JOIN users u1 ON pr.created_by = u1.id
        LEFT JOIN users u2 ON pr.reviewed_by = u2.id
        WHERE pr.area_id = :area_id
    """

    params = {'area_id': user_area.id}
    conditions = []

    if keyword:
        conditions.append("(pr.payment_number LIKE :keyword OR pr.summary LIKE :keyword OR pr.reference_number LIKE :keyword)")
        params['keyword'] = f'%{keyword}%'

    if supplier_id:
        conditions.append("pr.supplier_id = :supplier_id")
        params['supplier_id'] = supplier_id
    
    if payment_method:
        conditions.append("pr.payment_method = :payment_method")
        params['payment_method'] = payment_method

    if status:
        conditions.append("pr.status = :status")
        params['status'] = status

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            conditions.append("pr.payment_date >= :start_date")
            params['start_date'] = start_date_obj
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            conditions.append("pr.payment_date <= :end_date")
            params['end_date'] = end_date_obj
        except ValueError:
            pass

    # 构建完整SQL
    if conditions:
        base_sql += " AND " + " AND ".join(conditions)

    # 分页处理
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    offset = (page - 1) * per_page

    # 获取总数 - 不使用ORDER BY的子查询
    count_sql = f"SELECT COUNT(*) FROM ({base_sql}) as count_query"
    total = db.session.execute(text(count_sql), params).scalar()

    # 获取分页数据 - 在外层添加ORDER BY
    paginated_sql = f"{base_sql} ORDER BY pr.payment_date DESC, pr.created_at DESC OFFSET {offset} ROWS FETCH NEXT {per_page} ROWS ONLY"
    payments_data = db.session.execute(text(paginated_sql), params).fetchall()

    # 获取供应商列表用于筛选
    from app.models import Supplier
    supplier_sql = text("""
        SELECT DISTINCT s.id, s.name
        FROM suppliers s
        INNER JOIN payment_records pr ON s.id = pr.supplier_id
        WHERE pr.area_id = :area_id
        ORDER BY s.name
    """)
    suppliers = db.session.execute(supplier_sql, {'area_id': user_area.id}).fetchall()
    
    # 构建分页对象
    class PaginationMock:
        def __init__(self, items, total, page, per_page):
            self.items = items
            self.total = total
            self.page = page
            self.per_page = per_page
            self.pages = (total + per_page - 1) // per_page
            self.has_prev = page > 1
            self.has_next = page < self.pages
            self.prev_num = page - 1 if self.has_prev else None
            self.next_num = page + 1 if self.has_next else None

    payments = PaginationMock(payments_data, total, page, per_page)

    return render_template('financial/payments/index.html',
                         payments=payments,
                         suppliers=suppliers,
                         keyword=keyword,
                         supplier_id=supplier_id,
                         payment_method=payment_method,
                         status=status,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/payments/create', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('应付账款管理', 'payment')
def create_payment(user_area):
    """创建付款记录"""
    form = PaymentRecordForm()
    
    if form.validate_on_submit():
        try:
            # 获取应付账款信息
            payable = AccountPayable.query.get(form.payable_id.data)
            if not payable or payable.area_id != user_area.id:
                flash('应付账款不存在', 'danger')
                return render_template('financial/payments/form.html', form=form)
            
            # 检查付款金额
            if form.amount.data > payable.balance_amount:
                flash(f'付款金额不能超过应付账款余额 ¥{payable.balance_amount}', 'danger')
                return render_template('financial/payments/form.html', form=form)
            
            # 生成付款编号
            today = form.payment_date.data
            prefix = f"PAY{today.strftime('%Y%m%d')}"
            
            last_payment = PaymentRecord.query.filter(
                PaymentRecord.area_id == user_area.id,
                PaymentRecord.payment_number.like(f'{prefix}%')
            ).order_by(PaymentRecord.payment_number.desc()).first()
            
            if last_payment:
                last_number = int(last_payment.payment_number[-3:])
                payment_number = f"{prefix}{last_number + 1:03d}"
            else:
                payment_number = f"{prefix}001"
            
            # 使用 ORM 创建付款记录
            payment = PaymentRecord(
                payment_number=payment_number,
                area_id=user_area.id,
                payment_date=form.payment_date.data,
                amount=form.amount.data,
                payment_method=form.payment_method.data,
                payable_id=form.payable_id.data,
                supplier_id=payable.supplier_id,
                bank_account=form.bank_account.data,
                reference_number=form.reference_number.data,
                summary=form.summary.data,
                status='已确认',
                created_by=current_user.id,
                notes=form.notes.data
            )

            db.session.add(payment)
            db.session.flush()  # 获取ID但不提交
            payment_id = payment.id
            
            # 生成付款凭证
            # 生成凭证号
            voucher_prefix = f"PZ{today.strftime('%Y%m%d')}"
            last_voucher = FinancialVoucher.query.filter(
                FinancialVoucher.area_id == user_area.id,
                FinancialVoucher.voucher_number.like(f'{voucher_prefix}%')
            ).order_by(FinancialVoucher.voucher_number.desc()).first()

            if last_voucher:
                last_number = int(last_voucher.voucher_number[-3:])
                voucher_number = f"{voucher_prefix}{last_number + 1:03d}"
            else:
                voucher_number = f"{voucher_prefix}001"

            # 使用 ORM 创建付款凭证
            voucher = FinancialVoucher(
                voucher_number=voucher_number,
                voucher_date=form.payment_date.data,
                area_id=user_area.id,
                voucher_type='付款凭证',
                summary=f'付款给{payable.supplier.name}',
                total_amount=form.amount.data,
                status='已审核',
                source_type='付款记录',
                source_id=payment_id,
                created_by=current_user.id
            )

            db.session.add(voucher)
            db.session.flush()  # 获取ID但不提交
            voucher_id = voucher.id
            
            # 获取会计科目
            payable_subject = AccountingSubject.query.filter_by(
                area_id=user_area.id,
                code='2001'  # 应付账款
            ).first()
            
            # 根据付款方式选择科目
            if form.payment_method.data == '现金':
                payment_subject = AccountingSubject.query.filter_by(
                    area_id=user_area.id,
                    code='1001'  # 库存现金
                ).first()
            else:
                payment_subject = AccountingSubject.query.filter_by(
                    area_id=user_area.id,
                    code='1002'  # 银行存款
                ).first()
            
            if payable_subject and payment_subject:
                # 使用 ORM 生成凭证明细
                from app.models_financial import VoucherDetail

                # 借：应付账款
                detail1 = VoucherDetail(
                    voucher_id=voucher_id,
                    line_number=1,
                    subject_id=payable_subject.id,
                    summary=f'付款给{payable.supplier.name}',
                    debit_amount=form.amount.data,
                    credit_amount=0
                )
                db.session.add(detail1)

                # 贷：银行存款/库存现金
                detail2 = VoucherDetail(
                    voucher_id=voucher_id,
                    line_number=2,
                    subject_id=payment_subject.id,
                    summary=f'付款给{payable.supplier.name}',
                    debit_amount=0,
                    credit_amount=form.amount.data
                )
                db.session.add(detail2)
            
            # 更新付款记录的凭证ID
            payment.voucher_id = voucher_id
            
            db.session.commit()
            
            flash('付款记录创建成功', 'success')
            return redirect(url_for('financial.payments_index'))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建付款记录失败: {str(e)}")
            flash('创建失败，请重试', 'danger')
    
    return render_template('financial/payments/form.html', form=form)


@financial_bp.route('/payments/<int:id>')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def view_payment(id):
    """查看付款记录详情"""
    user_area = current_user.get_current_area()
    payment = PaymentRecord.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    return render_template('financial/payments/view.html', payment=payment)
