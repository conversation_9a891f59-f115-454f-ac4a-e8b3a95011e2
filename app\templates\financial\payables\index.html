{% extends "financial/base.html" %}

{% block page_title %}应付账款管理{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item active">应付账款管理</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.payables_pending_stock_ins') }}" class="uf-btn uf-btn-success">
    <i class="fas fa-plus uf-icon"></i> 从入库单生成
</a>
{% endblock %}

{% block financial_content %}
<!-- 用友财务软件风格搜索表单 -->
<div style="background: #f8f9fa; border: 1px solid #c0c0c0; padding: 8px; margin-bottom: 8px; border-radius: 1px;">
    <form method="GET" style="margin: 0;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 8px; align-items: end;">
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">关键词：</label>
                <input type="text" id="keyword" name="keyword" value="{{ keyword }}"
                       placeholder="应付账款号或发票号"
                       style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">供应商：</label>
                <select id="supplier_id" name="supplier_id" style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
                    <option value="">-- 所有供应商 --</option>
                    {% for supplier in suppliers %}
                    <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                        {{ supplier.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">状态：</label>
                <select id="status" name="status" style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
                    <option value="">-- 所有状态 --</option>
                    <option value="未付款" {% if status == '未付款' %}selected{% endif %}>未付款</option>
                    <option value="部分付款" {% if status == '部分付款' %}selected{% endif %}>部分付款</option>
                    <option value="已付清" {% if status == '已付清' %}selected{% endif %}>已付清</option>
                </select>
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">开始日期：</label>
                <input type="date" id="start_date" name="start_date" value="{{ start_date }}"
                       style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div>
                <label style="display: block; font-size: 11px; color: #333; margin-bottom: 2px;">结束日期：</label>
                <input type="date" id="end_date" name="end_date" value="{{ end_date }}"
                       style="width: 100%; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div style="display: flex; gap: 4px;">
                <button type="submit" class="uf-btn uf-btn-primary uf-btn-sm">
                    <i class="fas fa-search" style="font-size: 10px;"></i> 查询
                </button>
                <a href="{{ url_for('financial.payables_index') }}" class="uf-btn uf-btn-sm">
                    <i class="fas fa-undo" style="font-size: 10px;"></i> 重置
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 用友财务软件专业应付账款列表 -->
<div class="uf-card">
    <div class="uf-card-header">
        <span>
            <i class="fas fa-credit-card uf-icon"></i> 应付账款列表
            <span style="margin-left: 6px; padding: 1px 4px; background: var(--uf-info); color: white; border-radius: 1px; font-size: 10px;">
                共 {{ payables.total }} 条记录
            </span>
        </span>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        {% if payables.items %}
        <table class="uf-table" style="margin: 0;">
            <thead>
                <tr>
                    <th style="width: 110px;">应付账款号</th>
                    <th style="width: 130px;">供应商</th>
                    <th style="width: 100px;">发票号</th>
                    <th style="width: 90px;">原始金额</th>
                    <th style="width: 90px;">余额</th>
                    <th style="width: 70px;">状态</th>
                    <th style="width: 80px;">创建日期</th>
                    <th style="width: 80px;">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for payable in payables.items %}
                <tr>
                    <td class="text-center">
                        <code class="uf-code">{{ payable.payable_number }}</code>
                    </td>
                    <td class="text-left" style="font-size: 11px;">{{ payable.supplier.name if payable.supplier else '未知供应商' }}</td>
                    <td class="text-center" style="font-size: 11px;">
                        {% if payable.invoice_number %}
                        <code class="uf-code uf-code-warning">{{ payable.invoice_number }}</code>
                        {% else %}
                        <span style="color: #999;">-</span>
                        {% endif %}
                    </td>
                    <td class="uf-amount-col">{{ "%.2f"|format(payable.original_amount) }}</td>
                    <td class="uf-amount-col">{{ "%.2f"|format(payable.balance_amount) }}</td>
                    <td class="text-center">
                        {% if payable.status == '未付款' %}
                            <span style="background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 1px 3px; border-radius: 1px; font-size: 10px;">{{ payable.status }}</span>
                        {% elif payable.status == '部分付款' %}
                            <span style="background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 1px 3px; border-radius: 1px; font-size: 10px;">{{ payable.status }}</span>
                        {% elif payable.status == '已付清' %}
                            <span style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 1px 3px; border-radius: 1px; font-size: 10px;">{{ payable.status }}</span>
                        {% else %}
                            <span style="background: #f8f9fa; color: #495057; border: 1px solid #dee2e6; padding: 1px 3px; border-radius: 1px; font-size: 10px;">{{ payable.status }}</span>
                        {% endif %}
                    </td>
                    <td class="text-center" style="font-size: 11px;">{{ payable.created_at.strftime('%Y-%m-%d') if payable.created_at else '未知' }}</td>
                    <td class="text-center">
                        <div class="uf-btn-group">
                            {% if payable.balance_amount > 0 %}
                            <a href="{{ url_for('financial.create_payment') }}?payable_id={{ payable.id }}"
                               class="uf-btn uf-btn-sm uf-btn-success" title="付款" style="padding: 1px 4px;">
                                <i class="fas fa-money-bill" style="font-size: 10px;"></i>
                            </a>
                            {% endif %}
                            <button class="uf-btn uf-btn-sm uf-btn-info" title="查看详情"
                                    onclick="viewPayable({{ payable.id }})" style="padding: 1px 4px;">
                                <i class="fas fa-eye" style="font-size: 10px;"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr style="background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%); font-weight: 600;">
                    <td colspan="3" class="text-right" style="font-size: 11px;">合计：</td>
                    <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(payables.items|sum(attribute='original_amount')) }}</td>
                    <td class="uf-amount-col" style="font-weight: 600;">{{ "%.2f"|format(payables.items|sum(attribute='balance_amount')) }}</td>
                    <td colspan="3"></td>
                </tr>
            </tfoot>
        </table>

        <!-- 用友风格分页 -->
        {% if payables.pages > 1 %}
        <div class="uf-pagination">
            {% if payables.has_prev %}
            <span class="uf-page-item">
                <a class="uf-page-link" href="{{ url_for('financial.payables_index', page=payables.prev_num, keyword=keyword, supplier_id=supplier_id, status=status, start_date=start_date, end_date=end_date) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </span>
            {% endif %}

            {% for page_num in payables.iter_pages() %}
                {% if page_num %}
                    {% if page_num != payables.page %}
                    <span class="uf-page-item">
                        <a class="uf-page-link" href="{{ url_for('financial.payables_index', page=page_num, keyword=keyword, supplier_id=supplier_id, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                    </span>
                    {% else %}
                    <span class="uf-page-item active">
                        <span class="uf-page-link">{{ page_num }}</span>
                    </span>
                    {% endif %}
                {% else %}
                <span class="uf-page-item disabled">
                    <span class="uf-page-link">…</span>
                </span>
                {% endif %}
            {% endfor %}

            {% if payables.has_next %}
            <span class="uf-page-item">
                <a class="uf-page-link" href="{{ url_for('financial.payables_index', page=payables.next_num, keyword=keyword, supplier_id=supplier_id, status=status, start_date=start_date, end_date=end_date) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </span>
            {% endif %}
        </div>
        {% endif %}
        {% else %}
        <div class="uf-empty-state">
            <i class="fas fa-credit-card"></i>
            <p>暂无应付账款数据</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
function viewPayable(payableId) {
    ufShowMessage('查看应付账款详情功能待实现，ID: ' + payableId, 'info');
}
</script>
{% endblock %}
