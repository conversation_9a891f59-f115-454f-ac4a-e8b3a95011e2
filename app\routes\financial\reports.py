"""
财务报表路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app, make_response
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import AccountingSubject, FinancialVoucher, VoucherDetail, AccountPayable, PaymentRecord
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text, func
from datetime import datetime, date, timedelta
import json


class AgingSummary:
    """账龄汇总数据类"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


@financial_bp.route('/reports')
@login_required
@school_required
@check_permission('财务报表', 'view')
def reports_index(user_area):
    """财务报表首页"""
    return render_template('financial/reports/index.html')


@financial_bp.route('/reports/balance-sheet')
@login_required
@school_required
@check_permission('财务报表', 'view')
def balance_sheet(user_area):
    """资产负债表"""

    # 获取查询参数 - 将查询条件区域一行显示
    balance_date = request.args.get('balance_date', date.today().strftime('%Y-%m-%d'))

    try:
        balance_date_obj = datetime.strptime(balance_date, '%Y-%m-%d').date()
    except ValueError:
        balance_date_obj = date.today()
        balance_date = balance_date_obj.strftime('%Y-%m-%d')

    # 获取资产负债表数据
    balance_sheet_data = get_balance_sheet_data(user_area.id, balance_date_obj)

    return render_template('financial/reports/balance_sheet.html',
                         assets=balance_sheet_data.assets,
                         liabilities=balance_sheet_data.liabilities,
                         equity=balance_sheet_data.equity,
                         balance_sheet_data=balance_sheet_data,
                         balance_date=balance_date,
                         user_area=user_area)


@financial_bp.route('/reports/cost-analysis')
@login_required
@school_required
@check_permission('财务报表', 'view')
def cost_analysis(user_area):
    """成本分析报表"""

    # 获取查询参数 - 将查询条件区域一行显示
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    analysis_type = request.args.get('analysis_type', 'monthly')  # monthly, daily, ingredient

    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        end_date_obj = date.today()
        start_date_obj = end_date_obj.replace(day=1)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = end_date_obj.strftime('%Y-%m-%d')

    # 获取成本分析数据
    try:
        cost_analysis_data = get_cost_analysis_data(user_area.id, start_date_obj, end_date_obj, analysis_type)

        # 确保数据结构完整
        if not cost_analysis_data:
            cost_analysis_data = {'cost_data': [], 'total_cost': 0, 'analysis_type': analysis_type}

        return render_template('financial/reports/cost_analysis.html',
                             cost_analysis_data=cost_analysis_data,
                             start_date=start_date,
                             end_date=end_date,
                             analysis_type=analysis_type,
                             user_area=user_area)
    except Exception as e:
        current_app.logger.error(f"成本分析页面渲染失败: {str(e)}")
        # 返回空数据结构，避免模板错误
        cost_analysis_data = {'cost_data': [], 'total_cost': 0, 'analysis_type': analysis_type}
        return render_template('financial/reports/cost_analysis.html',
                             cost_analysis_data=cost_analysis_data,
                             start_date=start_date,
                             end_date=end_date,
                             analysis_type=analysis_type,
                             user_area=user_area)


@financial_bp.route('/reports/payables-aging')
@login_required
@school_required
@check_permission('财务报表', 'view')
def payables_aging(user_area):
    """应付账款账龄分析"""

    # 获取查询参数 - 将查询条件区域一行显示
    aging_date = request.args.get('aging_date', date.today().strftime('%Y-%m-%d'))
    supplier_id = request.args.get('supplier_id', type=int)

    try:
        aging_date_obj = datetime.strptime(aging_date, '%Y-%m-%d').date()
    except ValueError:
        aging_date_obj = date.today()
        aging_date = aging_date_obj.strftime('%Y-%m-%d')

    # 获取账龄分析数据
    aging_data = get_payables_aging_data(user_area.id, aging_date_obj, supplier_id)

    # 获取供应商列表用于筛选
    from app.models import Supplier
    supplier_sql = text(f"""
        SELECT DISTINCT s.id, s.name
        FROM suppliers s
        INNER JOIN account_payables ap ON s.id = ap.supplier_id
        WHERE ap.area_id = {user_area.id} AND ap.balance_amount > 0
        ORDER BY s.name
    """)
    suppliers = db.session.execute(supplier_sql).fetchall()

    return render_template('financial/reports/payables_aging.html',
                         aging_data=aging_data,
                         suppliers=suppliers,
                         aging_date=aging_date,
                         supplier_id=supplier_id,
                         user_area=user_area)

@financial_bp.route('/style-demo')
@login_required
@school_required
def style_demo(user_area):
    """用友财务样式演示页面"""
    return render_template('financial/style_demo.html')

@financial_bp.route('/admin/style-demo')
@login_required
def admin_style_demo():
    """后台管理 - 用友财务样式演示页面"""
    return render_template('financial/style_demo.html')


def get_balance_sheet_data(area_id, balance_date):
    """获取资产负债表数据"""
    try:
        # 使用字符串格式化避免参数绑定问题
        balance_sheet_sql = text(f"""
            WITH subject_balances AS (
                SELECT
                    s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id,
                    ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) as balance
                FROM accounting_subjects s
                LEFT JOIN voucher_details vd ON s.id = vd.subject_id
                LEFT JOIN financial_vouchers fv ON vd.voucher_id = fv.id
                WHERE (s.is_system = 1 OR s.area_id = {area_id})
                AND s.is_active = 1
                AND (fv.voucher_date IS NULL OR fv.voucher_date <= '{balance_date}')
                AND (fv.status IS NULL OR fv.status IN ('已审核', '已记账'))
                GROUP BY s.id, s.code, s.name, s.subject_type, s.balance_direction, s.level, s.parent_id
                HAVING ISNULL(SUM(vd.debit_amount - vd.credit_amount), 0) != 0
            )
            SELECT * FROM subject_balances
            WHERE subject_type IN ('资产', '负债', '所有者权益')
            ORDER BY subject_type, code
        """)

        results = db.session.execute(balance_sheet_sql).fetchall()

        # 分类整理数据
        assets = []
        liabilities = []
        equity = []

        total_assets = 0
        total_liabilities = 0
        total_equity = 0

        for row in results:
            balance = float(row.balance or 0)
            # 根据余额方向调整显示
            if row.balance_direction == '贷方' and balance < 0:
                balance = abs(balance)
            elif row.balance_direction == '借方' and balance < 0:
                balance = 0

            item = {
                'id': row.id,
                'code': row.code,
                'name': row.name,
                'level': row.level,
                'parent_id': row.parent_id,
                'balance': balance,
                'amount': balance  # 添加amount字段以兼容模板
            }

            if row.subject_type == '资产':
                assets.append(item)
                total_assets += balance
            elif row.subject_type == '负债':
                liabilities.append(item)
                total_liabilities += balance
            elif row.subject_type == '所有者权益':
                equity.append(item)
                total_equity += balance

        # 构建资产数据结构
        assets_data = {
            'current_assets': assets,
            'non_current_assets': [],
            'current_assets_total': total_assets,
            'non_current_assets_total': 0,
            'total_assets': total_assets
        }

        # 构建负债数据结构
        liabilities_data = {
            'current_liabilities': liabilities,
            'non_current_liabilities': [],
            'current_liabilities_total': total_liabilities,
            'non_current_liabilities_total': 0,
            'total_liabilities': total_liabilities
        }

        # 构建所有者权益数据结构
        equity_data = {
            'equity_items': equity,
            'total_equity': total_equity
        }

        # 返回包含所有数据的对象，同时支持旧的访问方式
        class BalanceSheetData:
            def __init__(self, assets, liabilities, equity):
                # 新的结构化数据
                self.current_assets = assets
                self.non_current_assets = []
                self.current_assets_total = total_assets
                self.non_current_assets_total = 0
                self.total_assets = total_assets

                self.current_liabilities = liabilities
                self.non_current_liabilities = []
                self.current_liabilities_total = total_liabilities
                self.non_current_liabilities_total = 0
                self.total_liabilities = total_liabilities

                self.equity_items = equity
                self.total_equity = total_equity

                # 旧的结构化数据（向后兼容）
                self.assets = assets_data
                self.liabilities = liabilities_data
                self.equity = equity_data
                self.balance_check = abs(total_assets - (total_liabilities + total_equity)) < 0.01

        return BalanceSheetData(assets, liabilities, equity)

    except Exception as e:
        current_app.logger.error(f"获取资产负债表数据失败: {str(e)}")

        class EmptyBalanceSheetData:
            def __init__(self):
                self.current_assets = []
                self.non_current_assets = []
                self.current_assets_total = 0
                self.non_current_assets_total = 0
                self.total_assets = 0

                self.current_liabilities = []
                self.non_current_liabilities = []
                self.current_liabilities_total = 0
                self.non_current_liabilities_total = 0
                self.total_liabilities = 0

                self.equity_items = []
                self.total_equity = 0

                self.assets = {'current_assets': [], 'non_current_assets': [], 'current_assets_total': 0, 'non_current_assets_total': 0, 'total_assets': 0}
                self.liabilities = {'current_liabilities': [], 'non_current_liabilities': [], 'current_liabilities_total': 0, 'non_current_liabilities_total': 0, 'total_liabilities': 0}
                self.equity = {'equity_items': [], 'total_equity': 0}
                self.balance_check = True

        return EmptyBalanceSheetData()


def get_cost_analysis_data(area_id, start_date, end_date, analysis_type):
    """获取成本分析数据"""
    try:
        if analysis_type == 'monthly':
            # 按月分析
            cost_sql = text(f"""
                SELECT
                    YEAR(si.stock_in_date) as year,
                    MONTH(si.stock_in_date) as month,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = {area_id}
                AND si.stock_in_date >= '{start_date}'
                AND si.stock_in_date <= '{end_date}'
                AND si.status = '已入库'
                GROUP BY YEAR(si.stock_in_date), MONTH(si.stock_in_date)
                ORDER BY year, month
            """)
        elif analysis_type == 'daily':
            # 按日分析
            cost_sql = text(f"""
                SELECT
                    si.stock_in_date,
                    SUM(si.total_cost) as total_cost,
                    COUNT(si.id) as stock_in_count,
                    AVG(si.total_cost) as avg_cost_per_stock_in
                FROM stock_ins si
                WHERE si.area_id = {area_id}
                AND si.stock_in_date >= '{start_date}'
                AND si.stock_in_date <= '{end_date}'
                AND si.status = '已入库'
                GROUP BY si.stock_in_date
                ORDER BY si.stock_in_date
            """)
        else:  # ingredient
            # 按食材分析
            cost_sql = text(f"""
                SELECT
                    i.name as ingredient_name,
                    i.category,
                    SUM(sii.quantity) as total_quantity,
                    SUM(sii.quantity * sii.unit_price) as total_cost,
                    AVG(sii.unit_price) as avg_unit_cost,
                    COUNT(DISTINCT si.id) as stock_in_count
                FROM stock_in_items sii
                INNER JOIN stock_ins si ON sii.stock_in_id = si.id
                INNER JOIN ingredients i ON sii.ingredient_id = i.id
                WHERE si.area_id = {area_id}
                AND si.stock_in_date >= '{start_date}'
                AND si.stock_in_date <= '{end_date}'
                AND si.status = '已入库'
                GROUP BY i.id, i.name, i.category
                ORDER BY total_cost DESC
            """)

        results = db.session.execute(cost_sql).fetchall()

        cost_data = []
        total_cost = 0

        for row in results:
            if analysis_type == 'monthly':
                cost_data.append({
                    'period': f"{row.year}年{row.month}月",
                    'total_cost': float(row.total_cost or 0),
                    'stock_in_count': row.stock_in_count,
                    'avg_cost': float(row.avg_cost_per_stock_in or 0)
                })
            elif analysis_type == 'daily':
                cost_data.append({
                    'period': row.stock_in_date.strftime('%Y-%m-%d'),
                    'total_cost': float(row.total_cost or 0),
                    'stock_in_count': row.stock_in_count,
                    'avg_cost': float(row.avg_cost_per_stock_in or 0)
                })
            else:  # ingredient
                cost_data.append({
                    'ingredient_name': row.ingredient_name,
                    'category': row.category,
                    'total_quantity': float(row.total_quantity or 0),
                    'total_cost': float(row.total_cost or 0),
                    'avg_unit_cost': float(row.avg_unit_cost or 0),
                    'stock_in_count': row.stock_in_count
                })

            total_cost += float(row.total_cost or 0)

        return {
            'cost_data': cost_data,
            'total_cost': total_cost,
            'analysis_type': analysis_type
        }

    except Exception as e:
        current_app.logger.error(f"获取成本分析数据失败: {str(e)}")
        return {'cost_data': [], 'total_cost': 0, 'analysis_type': analysis_type}


def get_payables_aging_data(area_id, aging_date, supplier_id=None):
    """获取应付账款账龄分析数据"""
    try:
        # 构建账龄分析SQL（使用字符串格式化避免参数绑定问题）
        supplier_condition = f" AND ap.supplier_id = {supplier_id}" if supplier_id else ""

        aging_sql = f"""
            SELECT
                ap.id,
                ap.payable_number,
                s.name as supplier_name,
                ap.original_amount,
                ap.paid_amount,
                ap.balance_amount,
                ap.due_date,
                si.stock_in_date,
                si.stock_in_number,
                DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), '{aging_date}') as aging_days,
                CASE
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), '{aging_date}') <= 30 THEN '30天以内'
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), '{aging_date}') <= 60 THEN '31-60天'
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), '{aging_date}') <= 90 THEN '61-90天'
                    WHEN DATEDIFF(day, ISNULL(ap.due_date, si.stock_in_date), '{aging_date}') <= 180 THEN '91-180天'
                    ELSE '180天以上'
                END as aging_group
            FROM account_payables ap
            INNER JOIN suppliers s ON ap.supplier_id = s.id
            INNER JOIN stock_ins si ON ap.stock_in_id = si.id
            WHERE ap.area_id = {area_id}
            AND ap.balance_amount > 0{supplier_condition}
            ORDER BY aging_days DESC, ap.balance_amount DESC
        """

        results = db.session.execute(text(aging_sql)).fetchall()

        # 分组统计
        aging_groups = {
            '30天以内': {'count': 0, 'amount': 0, 'items': []},
            '31-60天': {'count': 0, 'amount': 0, 'items': []},
            '61-90天': {'count': 0, 'amount': 0, 'items': []},
            '91-180天': {'count': 0, 'amount': 0, 'items': []},
            '180天以上': {'count': 0, 'amount': 0, 'items': []}
        }

        total_amount = 0
        total_count = 0

        for row in results:
            balance_amount = float(row.balance_amount or 0)
            aging_group = row.aging_group

            item = {
                'id': row.id,
                'payable_number': row.payable_number,
                'supplier_name': row.supplier_name,
                'original_amount': float(row.original_amount or 0),
                'paid_amount': float(row.paid_amount or 0),
                'balance_amount': balance_amount,
                'due_date': row.due_date,
                'stock_in_date': row.stock_in_date,
                'stock_in_number': row.stock_in_number,
                'aging_days': row.aging_days
            }

            aging_groups[aging_group]['count'] += 1
            aging_groups[aging_group]['amount'] += balance_amount
            aging_groups[aging_group]['items'].append(item)

            total_amount += balance_amount
            total_count += 1

        # 计算百分比
        for group in aging_groups.values():
            group['percentage'] = (group['amount'] / total_amount * 100) if total_amount > 0 else 0

        return {
            'aging_groups': aging_groups,
            'total_amount': total_amount,
            'total_count': total_count,
            'aging_date': aging_date
        }

    except Exception as e:
        current_app.logger.error(f"获取应付账款账龄分析数据失败: {str(e)}")
        return {
            'aging_groups': {},
            'total_amount': 0,
            'total_count': 0,
            'aging_date': aging_date
        }








def calculate_period_costs(area_id, start_date, end_date, expense_subjects):
    """计算期间成本"""
    # 查询期间凭证
    vouchers = FinancialVoucher.query.filter(
        FinancialVoucher.area_id == area_id,
        FinancialVoucher.voucher_date >= start_date,
        FinancialVoucher.voucher_date <= end_date,
        FinancialVoucher.status.in_(['已审核', '已记账'])
    ).all()

    voucher_ids = [v.id for v in vouchers]
    subject_costs = {}

    if voucher_ids:
        # 查询凭证明细
        details = VoucherDetail.query.filter(
            VoucherDetail.voucher_id.in_(voucher_ids)
        ).all()

        # 计算各科目发生额
        for detail in details:
            subject_id = detail.subject_id
            if subject_id not in subject_costs:
                subject_costs[subject_id] = 0

            # 费用科目：借方为成本增加
            subject_costs[subject_id] += float(detail.debit_amount or 0)

    # 为所有费用科目设置默认值
    for subject in expense_subjects:
        if subject.id not in subject_costs:
            subject_costs[subject.id] = 0

    return subject_costs


def build_cost_analysis_data(current_costs, last_costs, expense_subjects):
    """构建成本分析数据结构"""

    # 分类成本科目
    direct_cost_codes = ['5001', '5002', '5003']  # 食材成本、加工成本等
    indirect_cost_codes = ['5101', '5102', '5103', '5201', '5202']  # 人工、水电、折旧等

    direct_cost_details = []
    indirect_cost_details = []
    direct_cost_total = 0
    indirect_cost_total = 0
    direct_cost_total_last = 0
    indirect_cost_total_last = 0

    for subject in expense_subjects:
        current_amount = current_costs.get(subject.id, 0)
        last_amount = last_costs.get(subject.id, 0)
        change = current_amount - last_amount
        change_rate = (change / last_amount * 100) if last_amount > 0 else 0

        cost_item = {
            'name': subject.name,
            'amount': current_amount,
            'amount_last': last_amount,
            'change': change,
            'change_rate': change_rate
        }

        # 根据科目代码分类
        if any(subject.code.startswith(code) for code in direct_cost_codes):
            direct_cost_details.append(cost_item)
            direct_cost_total += current_amount
            direct_cost_total_last += last_amount
        else:
            indirect_cost_details.append(cost_item)
            indirect_cost_total += current_amount
            indirect_cost_total_last += last_amount

    # 计算总成本
    total_cost = direct_cost_total + indirect_cost_total
    total_cost_last = direct_cost_total_last + indirect_cost_total_last

    # 计算变动
    direct_cost_change = direct_cost_total - direct_cost_total_last
    indirect_cost_change = indirect_cost_total - indirect_cost_total_last
    total_cost_change = total_cost - total_cost_last

    # 计算变动率
    direct_cost_change_rate = (direct_cost_change / direct_cost_total_last * 100) if direct_cost_total_last > 0 else 0
    indirect_cost_change_rate = (indirect_cost_change / indirect_cost_total_last * 100) if indirect_cost_total_last > 0 else 0
    total_cost_change_rate = (total_cost_change / total_cost_last * 100) if total_cost_last > 0 else 0

    # 计算成本占比
    direct_cost_ratio = (direct_cost_total / total_cost * 100) if total_cost > 0 else 0
    indirect_cost_ratio = (indirect_cost_total / total_cost * 100) if total_cost > 0 else 0
    direct_cost_ratio_last = (direct_cost_total_last / total_cost_last * 100) if total_cost_last > 0 else 0
    indirect_cost_ratio_last = (indirect_cost_total_last / total_cost_last * 100) if total_cost_last > 0 else 0

    # 假设用餐人次（实际应该从用餐记录获取）
    meal_count = 1000  # 简化处理
    meal_count_last = 1000

    cost_per_meal = total_cost / meal_count if meal_count > 0 else 0
    cost_per_meal_last = total_cost_last / meal_count_last if meal_count_last > 0 else 0
    cost_per_meal_change = cost_per_meal - cost_per_meal_last
    cost_per_meal_change_rate = (cost_per_meal_change / cost_per_meal_last * 100) if cost_per_meal_last > 0 else 0

    return {
        'direct_cost_details': direct_cost_details,
        'indirect_cost_details': indirect_cost_details,
        'direct_cost_total': direct_cost_total,
        'direct_cost_total_last': direct_cost_total_last,
        'direct_cost_change': direct_cost_change,
        'direct_cost_change_rate': direct_cost_change_rate,
        'indirect_cost_total': indirect_cost_total,
        'indirect_cost_total_last': indirect_cost_total_last,
        'indirect_cost_change': indirect_cost_change,
        'indirect_cost_change_rate': indirect_cost_change_rate,
        'total_cost': total_cost,
        'total_cost_last': total_cost_last,
        'total_cost_change': total_cost_change,
        'total_cost_change_rate': total_cost_change_rate,
        'cost_per_meal': cost_per_meal,
        'cost_per_meal_last': cost_per_meal_last,
        'cost_per_meal_change': cost_per_meal_change,
        'cost_per_meal_change_rate': cost_per_meal_change_rate,
        'direct_cost_ratio': direct_cost_ratio,
        'indirect_cost_ratio': indirect_cost_ratio,
        'direct_cost_ratio_last': direct_cost_ratio_last,
        'indirect_cost_ratio_last': indirect_cost_ratio_last,
        'direct_cost_ratio_change': direct_cost_ratio - direct_cost_ratio_last,
        'indirect_cost_ratio_change': indirect_cost_ratio - indirect_cost_ratio_last
    }





@financial_bp.route('/reports/voucher-summary')
@login_required
@school_required
@check_permission('财务报表', 'view')
def voucher_summary(user_area):
    """凭证汇总表"""

    # 获取查询参数
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    voucher_type = request.args.get('voucher_type', '')
    
    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        end_date_obj = date.today()
        start_date_obj = end_date_obj.replace(day=1)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = end_date_obj.strftime('%Y-%m-%d')
    
    # 查询期间凭证统计
    query = db.session.query(
        FinancialVoucher.voucher_type,
        FinancialVoucher.status,
        func.count(FinancialVoucher.id).label('count'),
        func.sum(FinancialVoucher.total_amount).label('total_amount')
    ).filter(
        FinancialVoucher.area_id == user_area.id,
        FinancialVoucher.voucher_date >= start_date_obj,
        FinancialVoucher.voucher_date <= end_date_obj
    )

    # 如果指定了凭证类型，添加过滤条件
    if voucher_type:
        query = query.filter(FinancialVoucher.voucher_type == voucher_type)

    voucher_stats = query.group_by(
        FinancialVoucher.voucher_type,
        FinancialVoucher.status
    ).all()

    # 获取凭证列表
    vouchers_query = FinancialVoucher.query.filter(
        FinancialVoucher.area_id == user_area.id,
        FinancialVoucher.voucher_date >= start_date_obj,
        FinancialVoucher.voucher_date <= end_date_obj
    )

    if voucher_type:
        vouchers_query = vouchers_query.filter(FinancialVoucher.voucher_type == voucher_type)

    vouchers = vouchers_query.order_by(
        FinancialVoucher.voucher_date.desc(),
        FinancialVoucher.voucher_number.desc()
    ).all()

    # 计算汇总统计数据
    total_vouchers = len(vouchers)
    reviewed_vouchers = len([v for v in vouchers if v.status in ['已审核', '已记账']])
    pending_vouchers = len([v for v in vouchers if v.status == '待审核'])
    total_amount = sum([float(v.total_amount) for v in vouchers])

    # 按类型统计
    by_type = {}
    for voucher in vouchers:
        vtype = voucher.voucher_type
        if vtype not in by_type:
            by_type[vtype] = {'count': 0, 'amount': 0}
        by_type[vtype]['count'] += 1
        by_type[vtype]['amount'] += float(voucher.total_amount)

    # 计算百分比
    for vtype in by_type:
        by_type[vtype]['percentage'] = (by_type[vtype]['amount'] / total_amount * 100) if total_amount > 0 else 0

    # 按状态统计
    by_status = {}
    for voucher in vouchers:
        status = voucher.status
        if status not in by_status:
            by_status[status] = {'count': 0, 'amount': 0}
        by_status[status]['count'] += 1
        by_status[status]['amount'] += float(voucher.total_amount)

    # 计算百分比
    for status in by_status:
        by_status[status]['percentage'] = (by_status[status]['amount'] / total_amount * 100) if total_amount > 0 else 0

    # 创建汇总对象
    summary = AgingSummary(
        total_vouchers=total_vouchers,
        reviewed_vouchers=reviewed_vouchers,
        pending_vouchers=pending_vouchers,
        total_amount=total_amount,
        by_type=[AgingSummary(voucher_type=k, count=v['count'], amount=v['amount'], percentage=v['percentage']) for k, v in by_type.items()],
        by_status=[AgingSummary(status=k, count=v['count'], amount=v['amount'], percentage=v['percentage']) for k, v in by_status.items()]
    )

    return render_template('financial/reports/voucher_summary.html',
                         summary=summary,
                         vouchers=vouchers,
                         start_date=start_date,
                         end_date=end_date,
                         voucher_type=voucher_type,
                         user_area=user_area)


@financial_bp.route('/reports/export/<report_type>')
@login_required
@school_required
@check_permission('财务报表', 'export')
def export_report(report_type, user_area):
    """导出报表"""
    # 这里可以实现Excel导出功能
    # 暂时返回JSON格式
    
    if report_type == 'payables':
        # 导出应付账款
        payables = AccountPayable.query.filter_by(area_id=user_area.id).all()
        data = [payable.to_dict() for payable in payables]
    elif report_type == 'payables_aging':
        # 导出应付账款账龄分析
        analysis_date = request.args.get('analysis_date', date.today().strftime('%Y-%m-%d'))

        try:
            analysis_date_obj = datetime.strptime(analysis_date, '%Y-%m-%d').date()
        except ValueError:
            analysis_date_obj = date.today()

        # 获取应付账款数据
        payables = AccountPayable.query.options(
            db.joinedload(AccountPayable.supplier)
        ).filter(
            AccountPayable.area_id == user_area.id,
            AccountPayable.balance_amount > 0
        ).all()

        # 构建导出数据
        export_data = []
        for payable in payables:
            created_date = payable.created_at.date() if payable.created_at else analysis_date_obj
            days_outstanding = (analysis_date_obj - created_date).days
            supplier_name = payable.supplier.name if payable.supplier else '未知供应商'

            export_data.append({
                'supplier_name': supplier_name,
                'payable_number': payable.payable_number,
                'original_amount': float(payable.original_amount),
                'balance_amount': float(payable.balance_amount),
                'aging_days': days_outstanding,
                'created_date': created_date.strftime('%Y-%m-%d'),
                'analysis_date': analysis_date
            })

        data = {
            'report_type': '应付账款账龄分析',
            'analysis_date': analysis_date,
            'school': user_area.name,
            'payables_detail': export_data
        }
    elif report_type == 'detail_ledger':
        # 导出明细账
        subject_id = request.args.get('subject_id', type=int)
        start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))

        if not subject_id:
            return jsonify({'error': '请指定科目'}), 400

        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式错误'}), 400

        # 导入明细账查询函数
        from app.routes.financial.ledgers import get_subject_detail_records, calculate_opening_balance
        from app.models_financial import AccountingSubject

        # 获取科目信息
        subject = AccountingSubject.query.filter_by(
            id=subject_id,
            area_id=user_area.id
        ).first()

        if not subject:
            return jsonify({'error': '科目不存在'}), 404

        # 获取明细记录
        detail_records = get_subject_detail_records(user_area.id, subject_id, start_date_obj, end_date_obj)
        opening_balance = calculate_opening_balance(user_area.id, subject_id, start_date_obj)

        data = {
            'report_type': '明细账',
            'subject_code': subject.code,
            'subject_name': subject.name,
            'start_date': start_date,
            'end_date': end_date,
            'opening_balance': opening_balance,
            'school': user_area.name,
            'detail_records': detail_records
        }
    elif report_type == 'general_ledger':
        # 导出总账
        start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
        subject_type = request.args.get('subject_type', '')

        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式错误'}), 400

        # 导入总账查询函数
        from app.routes.financial.ledgers import get_general_ledger_summary

        # 获取总账数据
        general_ledger_data = get_general_ledger_summary(user_area.id, start_date_obj, end_date_obj, subject_type)

        data = {
            'report_type': '总账',
            'start_date': start_date,
            'end_date': end_date,
            'subject_type': subject_type or '全部',
            'school': user_area.name,
            'general_ledger_data': general_ledger_data
        }
    elif report_type == 'balance_detail':
        # 导出科目余额表
        balance_date = request.args.get('balance_date', date.today().strftime('%Y-%m-%d'))
        subject_type = request.args.get('subject_type', '')

        try:
            balance_date_obj = datetime.strptime(balance_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式错误'}), 400

        # 导入科目余额查询函数
        from app.routes.financial.ledgers import get_subject_balances

        # 获取科目余额数据
        balance_data = get_subject_balances(user_area.id, balance_date_obj, subject_type)

        data = {
            'report_type': '科目余额表',
            'balance_date': balance_date,
            'subject_type': subject_type or '全部',
            'school': user_area.name,
            'balance_data': balance_data
        }
    elif report_type == 'payments':
        # 导出付款记录
        payments = PaymentRecord.query.filter_by(area_id=user_area.id).all()
        data = [payment.to_dict() for payment in payments]
    elif report_type == 'cost_analysis':
        # 导出成本分析表
        # 获取查询参数
        start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))

        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            end_date_obj = date.today()
            start_date_obj = end_date_obj.replace(day=1)

        # 计算上期同期日期
        days_diff = (end_date_obj - start_date_obj).days
        last_end_date = start_date_obj - timedelta(days=1)
        last_start_date = last_end_date - timedelta(days=days_diff)

        # 查询费用科目
        expense_subjects = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            subject_type='费用',
            is_active=True
        ).order_by(AccountingSubject.code).all()

        # 计算成本数据
        current_costs = calculate_period_costs(user_area.id, start_date_obj, end_date_obj, expense_subjects)
        last_costs = calculate_period_costs(user_area.id, last_start_date, last_end_date, expense_subjects)
        cost_analysis = build_cost_analysis_data(current_costs, last_costs, expense_subjects)

        data = {
            'report_type': '成本分析表',
            'period': f'{start_date} 至 {end_date}',
            'school': user_area.name,
            'cost_analysis': cost_analysis
        }
    else:
        data = []
    
    response = make_response(json.dumps(data, ensure_ascii=False, indent=2))
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename={report_type}_report.json'
    
    return response
